# Hey Alfie

Your very own AI property manager, dedicated to digitalising your place, staying on top of admin, and tackling tedious tasks to give you property peace of mind.

🏠 **Currently in Alpha Testing - Coming Soon!**

[Sign up for updates](#) to be notified when we launch.

---

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

## Fonts

This project uses the Ryker font family, which is subject to the Desktop EULA 2.0 license.
The full license can be found at `public/fonts/ryker/licenses/Desktop-EULA-2.0.txt`.

## Icons

This project uses [Hugeicons](https://www.hugeicons.com) for its icon system. The icons are accessed through a private NPM registry. To use the icons:

1. Ensure you have the following in your `.npmrc`:

```
@hugeicons:registry=https://npm.hugeicons.com/
//npm.hugeicons.com/:_authToken=YOUR_AUTH_TOKEN
```

2. Install Hugeicons packages as needed:

```bash
npm install @hugeicons/react
```

Note: You'll need a valid Hugeicons authentication token to access the registry.

# Analytics Setup

The application uses Google Tag Manager (GTM) and Google Analytics (GA) for tracking user behavior and page views.

## Google Tag Manager (GTM)

GTM is already configured in the codebase with the container ID `GTM-TWJWB6MT`. The GTM script is loaded in the `<head>` section of the application, and the noscript fallback is placed immediately after the opening `<body>` tag in `src/app/layout.tsx`.

### Configuration

1. Access your GTM account at [tagmanager.google.com](https://tagmanager.google.com/)
2. Use the container ID `GTM-TWJWB6MT` to manage your tags

## Google Analytics (GA)

Google Analytics should be set up through GTM for best practices:

### Setting up GA through GTM

1. Log in to your GTM account
2. Click on "Tags" in the left sidebar
3. Click "New" to create a new tag
4. Choose "Google Analytics: GA4 Configuration" as the tag type
5. Enter your GA4 Measurement ID (e.g., G-XXXXXXXX)
6. Set the trigger to "All Pages"
7. Save and publish your container

## Analytics Utility Functions

The application includes utility functions for tracking events and page views in `src/utils/gtm.ts`:

- `trackPageView(path, title)`: Tracks page views
- `trackEvent(eventName, eventProps)`: Tracks custom events

## Usage

Page views are automatically tracked when the route changes using the `AnalyticsProvider` in `src/providers/AnalyticsProvider.tsx`.

To track custom events in your components:

```tsx
import { trackEvent } from '@/utils/gtm';

// In your component
const handleButtonClick = () => {
  trackEvent('button_click', {
    button_name: 'signup',
    page: '/home'
  });
};
```

## Debugging

To verify that GTM and GA are working correctly:

1. Install the [Google Tag Assistant](https://chrome.google.com/webstore/detail/tag-assistant-by-google/kejbdjndbnbjgmefkgdddjlbokphdefk) Chrome extension
2. Open your website and click on the Tag Assistant icon
3. Check if GTM and GA tags are firing correctly
