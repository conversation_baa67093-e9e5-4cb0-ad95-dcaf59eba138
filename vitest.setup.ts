import '@testing-library/jest-dom/vitest';
import { cleanup } from '@testing-library/react';

afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

vi.mock('nuqs/adapters/next/app', () => ({
  NuqsAdapter: ({ children }: { children: React.ReactNode }) => children,
}));

vi.mock('nuqs', async (importOriginal) => {
  const actual = await importOriginal();

  return {
    ...(actual && typeof actual === 'object' ? actual : {}),
    useQueryState: <T>(key: string, _parser?: any) => {
      let state: T | null = null;

      const setState = (value: T | ((prev: T | null) => T | null)) => {
        state = typeof value === 'function' ? (value as any)(state) : value;
      };

      return [state, setState] as const;
    },
  };
});
