import { Chat } from '@/types/chats';
import {
  CompleteSendMessageResponse,
  MessageListResponse,
  SendMessageResponse,
} from '@/types/messages';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { EntityType } from './entityLink';
import { BackendClient } from '@/api/BackendClient';
import { UploadedFile } from '@/types/file';

export interface ChatsListResponse {
  items: Chat[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export async function fetchChats({
  page = 1,
  size = 20,
  client,
}: {
  page?: number;
  size?: number;
  client: BackendClient;
}): Promise<ChatsListResponse> {
  const res = await client.get<ChatsListResponse>(getApiUrl(`${API_ENDPOINTS.CHATS}/`), {
    params: {
      page,
      size,
    },
  });
  if (!res.data) {
    throw new Error('Failed to fetch chat data');
  }
  return res.data;
}

export async function fetchMessages({
  chatId,
  page = 1,
  size = 30,
  client,
}: {
  chatId: number;
  page?: number;
  size?: number;
  client: BackendClient;
}): Promise<MessageListResponse> {
  const res = await client.get<MessageListResponse>(
    getApiUrl(`${API_ENDPOINTS.CHATS}/${chatId}/messages/`),
    {
      params: {
        page,
        size,
      },
    }
  );
  if (!res.data) {
    throw new Error('Failed to fetch messages');
  }
  return res.data;
}

export interface MessageContent {
  content: string;
  type: 'text';
  additionalData: {
    device: string;
    location: string;
  };
}

export interface SendMessageParams {
  chatId?: number;
  message: MessageContent;
  attachments?: Pick<UploadedFile, 'documentId'>[];
  client: BackendClient;
  linked_entity?: {
    entityType: EntityType | null;
    id: number | null;
  };
  onSendMessageResponse: (response: SendMessageResponse) => Promise<void>;
  onComplete: (response: CompleteSendMessageResponse) => Promise<void>;
  onError?: (error: unknown) => void;
  onStreamStart?: () => void;
  onStreamEnd?: () => void;
  onContent?: () => void;
}

function isCompleteSendMessageResponse(obj: unknown): obj is CompleteSendMessageResponse {
  const casted = obj as CompleteSendMessageResponse;
  return (
    casted &&
    casted.chatId !== undefined &&
    casted.systemMessageId !== undefined &&
    casted.userMessageId !== undefined &&
    casted.message?.content !== undefined &&
    casted.message?.type !== undefined
  );
}

export async function sendMessage({
  chatId,
  message,
  attachments,
  client,
  linked_entity,
  onComplete,
  onError,
  onStreamStart,
  onStreamEnd,
  onSendMessageResponse,
  onContent,
}: SendMessageParams): Promise<void> {
  const stream = await client.postForStream(getApiUrl(`/messages/stream`), {
    chatId,
    message,
    attachments,
    ...(linked_entity?.id && linked_entity?.entityType ? { linked_entity } : {}),
  });

  onStreamStart?.();

  const reader = stream.getReader();
  let buffer: Partial<CompleteSendMessageResponse> = {};

  try {
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      const lines = value.split('\n');
      for (const line of lines) {
        const match = line.match(/^data:\s*(.*)$/);
        if (match) {
          try {
            const parsed = JSON.parse(match[1]);
            switch (parsed?.type) {
              case 'stream_send_message_response':
                buffer = { ...buffer, ...parsed.data };
                await onSendMessageResponse(parsed.data);
                break;
              case 'final_data':
                buffer = { ...buffer, additionalData: parsed.data };
                break;
              case 'content': {
                const message = buffer.message ?? {
                  content: '',
                  type: 'text',
                };
                message.content = message.content + parsed.data;
                buffer = {
                  ...buffer,
                  message,
                };
                onContent?.();
                break;
              }
            }
            if (isCompleteSendMessageResponse(buffer)) {
              await onComplete(buffer);
            } else {
              console.log(`Waiting for message to be ready`);
            }
          } catch (e) {
            console.error('Failed to parse line from stream:', line, e);
            onError && onError(e);
          }
        } else if (line.trim().length > 0 && line.trim() !== 'event: end') {
          console.error('Could not parse line from stream', line);
          onError && onError(new Error('Could not parse line from stream: ' + line));
        }
      }
    }
  } finally {
    // Signal that streaming has ended
    onStreamEnd?.();
  }
}
