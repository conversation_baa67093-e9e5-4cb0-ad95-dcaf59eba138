import { getApiUrl, API_ENDPOINTS } from '@/constants/api';
import { BackendClient } from '@/api/BackendClient';

interface Job {
  id?: number;
  headline?: string;
  subTitle?: string;
  details?: string;
  urgency?: string;
  availability?: string;
  status: string;
  timestamp?: string;
}

export async function acceptJob({
  jobId,
  client,
}: {
  jobId: number;
  client: BackendClient;
}): Promise<void> {
  await client.post(getApiUrl(`${API_ENDPOINTS.JOBS}/${jobId}/user-accept/`));
}

export async function fetchJob({
  jobId,
  client,
}: {
  jobId: number;
  client: BackendClient;
}): Promise<Job> {
  const response = await client.get<Job>(getApiUrl(`${API_ENDPOINTS.JOBS}/${jobId}`));
  return response.data;
}
