import { ICreateTodoDto, ITodoDto, IUpdateTodoDto } from './types';
import { BackendClient } from '@/api/BackendClient';

export const createTodo = async (
  url: string,
  { arg }: { arg: { client: BackendClient; createTodoDto: ICreateTodoDto } }
): Promise<void> => {
  await arg.client.post<void>(url, arg.createTodoDto);
};

export const fetchTodos = async (url: string, client: BackendClient): Promise<ITodoDto[]> => {
  const response = await client.get<unknown>(url);
  const data = response.data as { items: ITodoDto[] };
  return data.items;
};

export const updateTodo = async (
  url: string,
  { arg }: { arg: { client: BackendClient; updateTodo: IUpdateTodoDto } }
): Promise<void> => {
  await arg.client.patch<void>(url, arg.updateTodo);
};
