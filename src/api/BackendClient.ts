import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

type Props = {
  getToken: () => Promise<string | null>;
  onQuotaLimitReached: () => void;
  onForbidden: () => void;
};

export class BackendClient {
  public constructor(private readonly props: Props) {}

  private get axios() {
    const instance = axios.create({
      validateStatus: (status) => {
        if (status === 429) {
          this.props.onQuotaLimitReached();
        } else if (status > 400 && status <= 403) {
          this.props.onForbidden();
        }
        return status >= 200 && status < 300;
      },
    });
    instance.interceptors.request.use(async (config) => {
      const token = await this.props.getToken();
      if (!token) {
        const abortController = new AbortController();
        abortController.abort({ reason: 'No token' });
        this.props.onForbidden();
        return {
          ...config,
          signal: abortController.signal,
        };
      }
      config.headers.setAuthorization(`Bear<PERSON> ${token}`);
      return config;
    });
    return instance;
  }

  public get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axios.get(url, { ...config, headers: { Accept: 'application/json' } });
  }

  public post<T>(
    url: string,
    data?: object,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.axios.post(url, data, {
      ...config,
      headers: { Accept: 'application/json' },
    });
  }

  public patch<T>(
    url: string,
    data?: object,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.axios.patch(url, data, {
      ...config,
      headers: { Accept: 'application/json' },
    });
  }

  public delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axios.delete(url, {
      ...config,
      headers: { Accept: 'application/json' },
    });
  }

  public async postForStream(url: string, data?: object): Promise<ReadableStream<string>> {
    const token = await this.props.getToken();
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.body) {
      throw new Error('No response body found');
    }

    if (response.status === 429) {
      this.props.onQuotaLimitReached();
      throw new Error('Quota limit reached');
    }

    return response.body.pipeThrough(new TextDecoderStream());
  }
}
