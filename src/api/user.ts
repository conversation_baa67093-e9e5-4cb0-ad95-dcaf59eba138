'use client';

import axios from 'axios';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';
import { BackendClient } from '@/api/BackendClient';

export enum UserPropertyRelationType {
  OwnerAndOccupier = 'ownerAndOccupier',
  Landlord = 'landlord',
  Tenant = 'tenant',
  ManagingProfessional = 'managingProfessional',
}

export interface UserDetails {
  id: number;
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string | null;
  phoneNumber: string | null;
  canUserAcceptJobs: boolean;
}

export interface UserUpdate {
  mainUsage: UserPropertyRelationType;
}

export interface GuestUserResponse {
  token: string;
  userId: number;
  expiresAt: string;
}

export interface GuestConvertRequest {
  email: string;
  firstName: string;
  lastName: string;
}

export interface GuestConvertResponse {
  userId: number;
  clerkId: string;
  signInToken: string;
}

export type UserDetailsError = {
  field?: 'firstName' | 'lastName' | 'email' | 'phoneNumber';
  errorMessage: string;
};

export async function getCurrentUser({ client }: { client: BackendClient }): Promise<UserDetails> {
  const response = await client.get<UserDetails>(getApiUrl(`${API_ENDPOINTS.USER}/`));

  if (!response.data) {
    throw new Error('Failed to fetch user details');
  }

  return response.data;
}

export async function createGuestUser(): Promise<GuestUserResponse> {
  const response = await axios.post<GuestUserResponse>(
    getApiUrl(`${API_ENDPOINTS.USER_GUEST}/`),
    {},
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.data) {
    throw new Error('Failed to create guest user');
  }

  if (!response.data.expiresAt) {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 14);
    response.data.expiresAt = expiresAt.toISOString();
  }

  return response.data;
}

export async function convertGuestUser({
  request,
  client,
}: {
  request: GuestConvertRequest;
  client: BackendClient;
}): Promise<GuestConvertResponse | UserDetailsError> {
  if (request.firstName.length < 1) {
    return {
      field: 'firstName',
      errorMessage: 'Please provide your first name',
    };
  }

  if (request.lastName.length < 1) {
    return {
      field: 'lastName',
      errorMessage: 'Please provide your last name',
    };
  }

  try {
    const response = await client.post<GuestConvertResponse>(
      getApiUrl(`${API_ENDPOINTS.USER_GUEST}/convert/`),
      request
    );

    if (!response.data) {
      return { errorMessage: 'Failed to convert guest user - no response data' };
    }

    if (!response.data.signInToken || !response.data.clerkId) {
      return { errorMessage: 'Failed to convert guest user - missing required fields in response' };
    }

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const data = error.response?.data;
      if (Array.isArray(data?.detail) && data.detail.length > 0) {
        const detail = data.detail[0];
        const errorMessage = detail.ctx?.reason || detail.msg || 'An error occurred';
        let field: UserDetailsError['field'] = undefined;
        if (detail.loc?.length === 2 && detail.loc[1] === 'email') {
          field = 'email';
        }
        return {
          field,
          errorMessage,
        };
      } else if (typeof data?.detail === 'string') {
        return {
          errorMessage: data.detail,
        };
      } else if (data?.message) {
        return {
          errorMessage: `Server error: ${data.message}`,
        };
      } else if (error.response?.status) {
        return {
          errorMessage: error.response.statusText,
        };
      } else if (error.request) {
        return {
          errorMessage: 'Network error: No response from server',
        };
      }
    }

    throw error;
  }
}
