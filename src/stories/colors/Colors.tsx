import React from "react";
import "./colors.css";

interface ColorItem {
  name: string;
  hex: string;
}

interface ColorSectionProps {
  title: string;
  colors: ColorItem[];
}

const ColorSection: React.FC<ColorSectionProps> = ({ title, colors }) => (
  <div className="color-section">
    <h3>{title}</h3>
    <div className="color-grid">
      {colors.map((color) => (
        <div key={color.name} className="color-item">
          <div
            className="color-swatch"
            style={{ backgroundColor: color.hex }}
          />
          <div className="color-info">
            <span className="color-name">{color.name}</span>
            <span className="color-hex">{color.hex}</span>
          </div>
        </div>
      ))}
    </div>
  </div>
);

export const Colors: React.FC = () => {
  const primitiveColors = {
    primary: [
      { name: "Yellow 30", hex: "#D28E28" },
      { name: "Yellow 20", hex: "#E3A13E" },
      { name: "Yellow 10", hex: "#F7BC64" },
    ],
    secondary: [
      { name: "Purple 30", hex: "#23063A" },
      { name: "<PERSON> 20", hex: "#63318B" },
      { name: "Purple 10", hex: "#743F9E" },
      { name: "Green 30", hex: "#0E372C" },
      { name: "Green 20", hex: "#1B6E5A" },
      { name: "Green 10", hex: "#3E9681" },
      { name: "Red 30", hex: "#35000D" },
      { name: "Red 20", hex: "#991039" },
      { name: "Red 10", hex: "#C11E4F" },
      { name: "Blue 30", hex: "#022034" },
      { name: "Blue 20", hex: "#21587E" },
      { name: "Blue 10", hex: "#31709B" },
    ],
    neutrals: [
      { name: "Dark 40", hex: "#342B25" },
      { name: "Dark 30", hex: "#4A403A" },
      { name: "Dark 20", hex: "#615853" },
      { name: "Dark 10", hex: "#756B66" },
      { name: "Neutral 30", hex: "#DFD8CB" },
      { name: "Neutral 20", hex: "#F0E8DC" },
      { name: "Neutral 10", hex: "#F8F3E9" },
    ],
  };

  const semanticColors = {
    default: [
      { name: "Background/Primary", hex: "#F8F3E9" },
      { name: "Background/Secondary", hex: "#F0E8DC" },
      { name: "Background/Tertiary", hex: "#DFD8CB" },
      { name: "Border/Primary", hex: "#DFD8CB" },
      { name: "Border/Secondary", hex: "#4A403A" },
      { name: "Text/Primary", hex: "#342B25" },
      { name: "Text/Secondary", hex: "#615853" },
      { name: "Text/Tertiary", hex: "#756B66" },
      { name: "Text/Link", hex: "#21587E" },
    ],
    yellow: [
      { name: "Background/Primary", hex: "#D28E28" },
      { name: "Background/Secondary", hex: "#F7BC64" },
      { name: "Background/Tertiary", hex: "#F8F3E9" },
      { name: "Border/Primary", hex: "#756B66" },
      { name: "Border/Secondary", hex: "#DFD8CB" },
      { name: "Text/Primary", hex: "#342B25" },
      { name: "Text/Secondary", hex: "#4A403A" },
      { name: "Text/Tertiary", hex: "#615853" },
      { name: "Text/Link", hex: "#21587E" },
    ],
  };

  return (
    <div className="colors-container">
      <div className="section-title">
        <h2>Colour Variables</h2>
      </div>

      <div className="primitive-section">
        <h2>Primitive</h2>
        <p>
          Primitive colors are the foundational building blocks of a design
          system&apos;s color palette. They serve as the base for more specific
          design decisions.
        </p>
      </div>

      <ColorSection title="Primary" colors={primitiveColors.primary} />
      <ColorSection title="Secondary" colors={primitiveColors.secondary} />
      <ColorSection title="Neutrals" colors={primitiveColors.neutrals} />

      <div className="primitive-section">
        <h2>Semantic</h2>
        <p>
          Semantic colors are variables where the names describe the purpose or
          meaning of the color within the design rather than describing the
          color itself.
        </p>
      </div>

      <ColorSection title="Default Theme" colors={semanticColors.default} />
      <ColorSection title="Yellow Theme" colors={semanticColors.yellow} />
    </div>
  );
};
