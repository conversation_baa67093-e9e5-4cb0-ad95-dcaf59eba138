import React from "react";
import { ColorSection } from "./ColorComponents";
import "./colors.css";

export const SemanticColors: React.FC = () => {
  const semanticColors = {
    default: [
      { name: "Background/Primary", hex: "#F8F3E9" },
      { name: "Background/Secondary", hex: "#F0E8DC" },
      { name: "Background/Tertiary", hex: "#DFD8CB" },
      { name: "Border/Primary", hex: "#DFD8CB" },
      { name: "Border/Secondary", hex: "#4A403A" },
      { name: "Text/Primary", hex: "#342B25" },
      { name: "Text/Secondary", hex: "#615853" },
      { name: "Text/Tertiary", hex: "#756B66" },
      { name: "Text/Link", hex: "#21587E" },
    ],
    yellow: [
      { name: "Background/Primary", hex: "#D28E28" },
      { name: "Background/Secondary", hex: "#F7BC64" },
      { name: "Background/Tertiary", hex: "#F8F3E9" },
      { name: "Border/Primary", hex: "#756B66" },
      { name: "Border/Secondary", hex: "#DFD8CB" },
      { name: "Text/Primary", hex: "#342B25" },
      { name: "Text/Secondary", hex: "#4A403A" },
      { name: "Text/Tertiary", hex: "#615853" },
      { name: "Text/Link", hex: "#21587E" },
    ],
  };

  return (
    <div className="colors-container">
      <div className="section-title">
        <h2>Semantic Colors</h2>
      </div>

      <div className="primitive-section">
        <p>
          Semantic colors are variables where the names describe the purpose or
          meaning of the color within the design rather than describing the
          color itself.
        </p>
      </div>

      <ColorSection title="Default Theme" colors={semanticColors.default} />
      <ColorSection title="Yellow Theme" colors={semanticColors.yellow} />
    </div>
  );
};
