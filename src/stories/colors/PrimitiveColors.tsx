import React from "react";
import { ColorSection } from "./ColorComponents";
import "./colors.css";

export const PrimitiveColors: React.FC = () => {
  const primitiveColors = {
    primary: [
      { name: "Yellow 30", hex: "#D28E28" },
      { name: "Yellow 20", hex: "#E3A13E" },
      { name: "Yellow 10", hex: "#F7BC64" },
    ],
    secondary: [
      { name: "Purple 30", hex: "#23063A" },
      { name: "Purple 20", hex: "#63318B" },
      { name: "Purple 10", hex: "#743F9E" },
      { name: "Green 30", hex: "#0E372C" },
      { name: "Green 20", hex: "#1B6E5A" },
      { name: "Green 10", hex: "#3E9681" },
      { name: "Red 30", hex: "#35000D" },
      { name: "Red 20", hex: "#991039" },
      { name: "Red 10", hex: "#C11E4F" },
      { name: "Blue 30", hex: "#022034" },
      { name: "Blue 20", hex: "#21587E" },
      { name: "Blue 10", hex: "#31709B" },
    ],
    neutrals: [
      { name: "Dark 40", hex: "#342B25" },
      { name: "Dark 30", hex: "#4A403A" },
      { name: "Dark 20", hex: "#615853" },
      { name: "Dark 10", hex: "#756B66" },
      { name: "Neutral 30", hex: "#DFD8CB" },
      { name: "Neutral 20", hex: "#F0E8DC" },
      { name: "Neutral 10", hex: "#F8F3E9" },
    ],
  };

  return (
    <div className="colors-container">
      <div className="section-title">
        <h2>Primitive Colors</h2>
      </div>

      <div className="primitive-section">
        <p>
          Primitive colors are the foundational building blocks of a design
          system&apos;s color palette. They serve as the base for more specific
          design decisions.
        </p>
      </div>

      <ColorSection title="Primary" colors={primitiveColors.primary} />
      <ColorSection title="Secondary" colors={primitiveColors.secondary} />
      <ColorSection title="Neutrals" colors={primitiveColors.neutrals} />
    </div>
  );
};
