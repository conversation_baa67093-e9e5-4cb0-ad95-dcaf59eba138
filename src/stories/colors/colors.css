.colors-container {
  padding: 24px;
  font-family: 'Nuni<PERSON> Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.section-title {
  margin-bottom: 32px;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.section-title p {
  color: #666;
  font-size: 14px;
}

.primitive-section {
  margin-top: 48px;
  margin-bottom: 32px;
}

.primitive-section h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.primitive-section p {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.color-section {
  margin-bottom: 32px;
}

.color-section h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-swatch {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.color-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.color-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.color-hex {
  font-size: 12px;
  color: #666;
  font-family: monospace;
} 