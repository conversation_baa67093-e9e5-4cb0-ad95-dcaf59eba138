import React from "react";
import "./colors.css";

export interface ColorItem {
  name: string;
  hex: string;
}

interface ColorSectionProps {
  title: string;
  colors: ColorItem[];
}

export const ColorSection: React.FC<ColorSectionProps> = ({
  title,
  colors,
}) => (
  <div className="color-section">
    <h3>{title}</h3>
    <div className="color-grid">
      {colors.map((color) => (
        <div key={color.name} className="color-item">
          <div
            className="color-swatch"
            style={{ backgroundColor: color.hex }}
          />
          <div className="color-info">
            <span className="color-name">{color.name}</span>
            <span className="color-hex">{color.hex}</span>
          </div>
        </div>
      ))}
    </div>
  </div>
);
