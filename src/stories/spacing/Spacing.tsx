import React from "react";
import { SpacingTable, SpacingItem } from "./SpacingComponents";
import "./spacing.css";

export const Spacing: React.FC = () => {
  const spacings: SpacingItem[] = [
    { name: "0", size: "0px", pixels: "0px" },
    { name: "px", size: "0.0625rem", pixels: "1px" },
    { name: "0.5", size: "0.125rem", pixels: "2px" },
    { name: "1", size: "0.25rem", pixels: "4px" },
    { name: "1.5", size: "0.375rem", pixels: "6px" },
    { name: "2", size: "0.5rem", pixels: "8px" },
    { name: "2.5", size: "0.625rem", pixels: "10px" },
    { name: "3", size: "0.75rem", pixels: "12px" },
    { name: "3.5", size: "0.875rem", pixels: "14px" },
    { name: "4", size: "1rem", pixels: "16px" },
    { name: "5", size: "1.25rem", pixels: "20px" },
    { name: "6", size: "1.5rem", pixels: "24px" },
    { name: "7", size: "1.75rem", pixels: "28px" },
    { name: "8", size: "2rem", pixels: "32px" },
    { name: "9", size: "2.25rem", pixels: "36px" },
    { name: "10", size: "2.5rem", pixels: "40px" },
    { name: "11", size: "2.75rem", pixels: "44px" },
    { name: "12", size: "3rem", pixels: "48px" },
    { name: "14", size: "3.5rem", pixels: "56px" },
    { name: "16", size: "4rem", pixels: "64px" },
    { name: "20", size: "5rem", pixels: "80px" },
    { name: "24", size: "6rem", pixels: "96px" },
    { name: "28", size: "7rem", pixels: "112px" },
    { name: "32", size: "8rem", pixels: "128px" },
    { name: "36", size: "9rem", pixels: "144px" },
    { name: "40", size: "10rem", pixels: "160px" },
    { name: "44", size: "11rem", pixels: "176px" },
    { name: "48", size: "12rem", pixels: "192px" },
    { name: "52", size: "13rem", pixels: "208px" },
    { name: "56", size: "14rem", pixels: "224px" },
    { name: "60", size: "15rem", pixels: "240px" },
    { name: "64", size: "16rem", pixels: "256px" },
    { name: "72", size: "18rem", pixels: "288px" },
    { name: "80", size: "20rem", pixels: "320px" },
    { name: "96", size: "24rem", pixels: "384px" },
  ];

  return (
    <div className="spacing-container">
      <div className="section-title">
        <h2>Spacing Scale</h2>
        <p>
          A comprehensive set of spacing values to maintain consistent layout
          and component spacing throughout the design system.
        </p>
      </div>
      <SpacingTable spacings={spacings} />
    </div>
  );
};
