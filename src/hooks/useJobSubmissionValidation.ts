import { useMemo } from 'react';
import { useUser } from '@clerk/nextjs';
import { useWidgets } from '@/hooks/useWidgets';

export default function useJobSubmissionValidation() {
  const { user, isLoaded } = useUser();
  const { addressValue } = useWidgets();

  const phoneNumber = useMemo(() => {
    if (user?.primaryPhoneNumber) {
      return user.primaryPhoneNumber.phoneNumber;
    }
    if (user?.phoneNumbers && user?.phoneNumbers?.length > 0) {
      return user.phoneNumbers[0].phoneNumber;
    }
    return null;
  }, [user]);

  const areRequiredFieldsFilled = useMemo(() => {
    if (!isLoaded || !user) return false;
    const firstName = user.firstName?.trim();
    const lastName = user.lastName?.trim();
    const email = user.primaryEmailAddress?.emailAddress?.trim();
    const phone = phoneNumber?.trim();
    const address = addressValue?.trim();
    return Boolean(firstName && lastName && email && phone && address);
  }, [isLoaded, user, phoneNumber, addressValue]);

  return {
    areRequiredFieldsFilled,
  };
}