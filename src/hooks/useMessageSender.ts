'use client';

import { useRouter } from 'next/navigation';
import useChatParams from '@/hooks/useChatParams';
import { useChats } from './useChats';
import { useCallback } from 'react';
import useBackendClient from '@/hooks/useBackendClient';
import { UploadedFile } from '@/types/file';

export default function useMessageSender() {
  const router = useRouter();
  const { chatId } = useChatParams();
  const { sendMessage } = useChats();
  const { client } = useBackendClient();

  const wrappedSendMessage = useCallback(
    async (
      value: string,
      attachments?: UploadedFile[],
      linkedEntity?: { entityType: 'chats' | 'todos'; id: number }
    ): Promise<boolean> => {
      try {
        console.log('useMessageSender sending a message');
        await sendMessage(
          chatId,
          value,
          client,
          attachments,
          (newChatId) => {
            console.log(`useMessageSender router.replace('/chats/${newChatId}')`);

            chatId ? router.replace(`/chats/${chatId}`) : router.replace(`/chats/${newChatId}`);
          },
          linkedEntity
        );
        return true;
      } catch (error) {
        console.error('Failed to send message:', error);
      }
      return false;
    },
    [chatId, client, router, sendMessage]
  );

  return {
    sendMessage: wrappedSendMessage,
  };
}
