import { useUser } from '@clerk/nextjs';
import { useCallback, useState } from 'react';
import { UpdateUserParams } from '@clerk/types/dist/user';
import { UserDetailsError } from '@/api/user';

export default function useClerkDetailsHandler() {
  const { user } = useUser();
  const [error, setError] = useState<UserDetailsError>();

  const setPhone = useCallback(
    async (phoneNumber: string) => {
      if (!user) return;

      for (const existingPhone of user.phoneNumbers) {
        await existingPhone.destroy();
      }
      try {
        await user.createPhoneNumber({ phoneNumber });
      } catch (e: any) {
        const message =
          e.message === '`phone_number` must be a `phone_number`.'
            ? 'Please enter a phone number starting with +44 or another country code'
            : e.message;
        setError({
          field: 'phoneNumber',
          errorMessage: message,
        });
        throw e;
      }
      await user.reload();
    },
    [user]
  );

  const setEmailAddress = useCallback(
    async (email: string) => {
      if (!user) return;

      let resource = user.emailAddresses?.find(
        (emailAddress) => emailAddress.emailAddress === email
      );
      try {
        if (!resource) {
          resource = await user.createEmailAddress({ email });
        }
        if (!resource) throw new Error('Failed to create an email address');
        for (const emailAddress of user.emailAddresses) {
          if (emailAddress.id === resource.id) continue;
          await emailAddress.destroy();
        }
      } catch (e: any) {
        const message =
          e.message === 'email_address must be a valid email address.'
            ? 'Please enter a valid email address.'
            : e.message;
        setError({
          field: 'email',
          errorMessage: message,
        });
        throw e;
      }
      await user.reload();
    },
    [user]
  );

  const setDetail = useCallback(
    async (detailName: keyof UpdateUserParams, detailValue: string) => {
      if (user) {
        await user.update({ [detailName]: detailValue });
        await user.reload();
      }
    },
    [user]
  );

  return {
    error,
    setPhone,
    setEmailAddress,
    setFirstName: (value: string) => setDetail('firstName', value),
    setLastName: (value: string) => setDetail('lastName', value),
  };
}
