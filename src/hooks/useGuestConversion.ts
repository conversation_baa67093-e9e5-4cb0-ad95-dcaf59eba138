import { useCallback, useState } from 'react';
import { useAuth, useClerk } from '@clerk/nextjs';
import { convertGuestUser, UserDetailsError, GuestConvertRequest } from '@/api/user';
import { useAuthStore } from '@/stores/auth.store';
import { create } from 'zustand';
import useBackendClient from '@/hooks/useBackendClient';

interface ConversionState {
  isConverting: boolean;
  setIsConverting: (converting: boolean) => void;
}

export const useConversionState = create<ConversionState>((set) => ({
  isConverting: false,
  setIsConverting: (converting: boolean) => set({ isConverting: converting }),
}));

interface UseGuestConversionReturn {
  convertGuest: (data: GuestConvertRequest) => Promise<boolean>;
  isConverting: boolean;
  error: UserDetailsError | null;
  currentStep: 'idle' | 'converting' | 'complete';
}

export const useGuestConversion = (): UseGuestConversionReturn => {
  const [isConverting, setIsConverting] = useState(false);
  const [error, setError] = useState<UseGuestConversionReturn['error']>(null);
  const [currentStep, setCurrentStep] = useState<'idle' | 'converting' | 'complete'>('idle');
  const { isSignedIn } = useAuth();
  const clerk = useClerk();
  const { clearGuestAuth } = useAuthStore();
  const { setIsConverting: setGlobalConverting } = useConversionState();
  const { client } = useBackendClient();

  const convertGuest = useCallback(
    async (data: GuestConvertRequest): Promise<boolean> => {
      if (isSignedIn) {
        return true;
      }

      setIsConverting(true);
      setGlobalConverting(true);
      setError(null);
      setCurrentStep('converting');

      const response = await convertGuestUser({
        request: data,
        client,
      });

      setIsConverting(false);
      setGlobalConverting(false);

      if ('errorMessage' in response) {
        const errorMessage = response.errorMessage;

        if (errorMessage.includes('Guest user already has a Clerk ID')) {
          clearGuestAuth();
          setCurrentStep('complete');
          setGlobalConverting(false);
          return true;
        } else {
          setError(response);
          setCurrentStep('idle');
          return false;
        }
      } else {
        if (!response.signInToken || !response.clerkId) {
          throw new Error('Failed to convert guest user - invalid response from server');
        }

        const signInResult = await clerk.client.signIn.create({
          strategy: 'ticket',
          ticket: response.signInToken,
        });

        if (signInResult.status === 'complete') {
          await clerk.setActive({ session: signInResult.createdSessionId });

          try {
            const currentUser = clerk.user;
            if (currentUser) {
              await currentUser.update({
                unsafeMetadata: {
                  ...currentUser.unsafeMetadata,
                  visitedOnboarding: true,
                },
              });
              await currentUser.reload();
            } else {
            }
          } catch (metadataError) {
            console.error('Error setting visitedOnboarding flag:', metadataError);
          }
        } else {
          throw new Error('Sign-in not completed');
        }

        clearGuestAuth();

        setCurrentStep('complete');
        setGlobalConverting(false);
        return true;
      }
    },
    [isSignedIn, setGlobalConverting, client, clearGuestAuth, clerk]
  );

  return {
    convertGuest,
    isConverting,
    error,
    currentStep,
  };
};
