import useBackendClient from '@/hooks/useBackendClient';
import { DocumentDto } from '@/api/documents';
import { useCallback } from 'react';
import { UniversalFile } from '@/types/file';
import { cached } from '@/utils/cached';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';

export type DocumentFetcher = (input: DocumentDto | UniversalFile) => Promise<Blob>;

export default function useFileBlobFetcher(): DocumentFetcher {
  const { client } = useBackendClient();

  return useCallback(
    async (input: DocumentDto | UniversalFile): Promise<Blob> => {
      if ('blob' in input) {
        return input.blob;
      }

      const documentId = 'documentId' in input ? input.documentId : input.id;
      return await cached(documentId, async (): Promise<Blob> => {
        const response = await client.get<Blob>(
          getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/${documentId}/`),
          {
            responseType: 'blob',
          }
        );
        return response.data;
      });
    },
    [client]
  );
}
