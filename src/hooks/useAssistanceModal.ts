import { create } from 'zustand';
import useBackendClient from '@/hooks/useBackendClient';
import { useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';

type ModalType = 'please-sign-up' | 'user-quota-exceeded';

interface Store {
  currentlyOpenModal: ModalType | undefined;
  openModal: (type: ModalType) => void;
  closeModal: () => void;
}

const useStore = create<Store>((set) => ({
  currentlyOpenModal: undefined,
  openModal: (type) => {
    set({ currentlyOpenModal: type });
  },
  closeModal: () => set({ currentlyOpenModal: undefined }),
}));

export function useAssistanceModal() {
  const { currentlyOpenModal, openModal, closeModal } = useStore();
  const { isForbidden, isQuotaLimitReached } = useBackendClient();
  const { isSignedIn } = useAuth();

  useEffect(() => {
    if (isForbidden) {
      openModal('please-sign-up');
    }
  }, [isForbidden, openModal]);

  useEffect(() => {
    if (isQuotaLimitReached) {
      if (isSignedIn) {
        openModal('user-quota-exceeded');
      }
      else {
        openModal('please-sign-up');
      }
    }
  }, [isForbidden, isQuotaLimitReached, isSignedIn, openModal]);

  return {
    currentlyOpenModal,
    openModal,
    closeModal,
  };
}