import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { fetchMessages } from '@/api/chats';
import { fetchJob } from '@/api/jobs';
import { useChats } from './useChats';
import { BackendClient } from '@/api/BackendClient';

export interface MessageState {
  isLoading: boolean;
  hasMore: boolean;
  currentPage: number;
  jobSummaryConfirmed: boolean;
  showItLooksGoodMessage: boolean;
  userDetailsValidated: boolean;
  fetchMessages: (
    chatId: number,
    client: BackendClient,
    page?: number
  ) => Promise<{ isValid: boolean; hasMessages: boolean }>;
  loadMoreMessages: (chatId: number, client: BackendClient) => Promise<void>;
  setJobSummaryConfirmed: (confirmed: boolean) => void;
  setShowItLooksGoodMessage: (show: boolean) => void;
  setUserDetailsValidated: (validated: boolean) => void;
  resetStore: () => void;
}

export const useMessages = create<MessageState>()(
  persist(
    (set, get) => ({
      isLoading: false,
      hasMore: true,
      currentPage: 1,
      jobSummaryConfirmed: false,
      showItLooksGoodMessage: false,
      userDetailsValidated: false,
      fetchMessages: async (chatId, client, page = 1) => {
        if (!chatId) return { isValid: false, hasMessages: false };
        set({ isLoading: true });
        try {
          const response = await fetchMessages({
            chatId,
            page,
            client,
          });

          const isValid = response && Array.isArray(response.items);
          const hasMessages = isValid && response.items.length > 0;

          if (isValid && hasMessages) {
            useChats.getState().replaceMessages(response.items, chatId);
            set({
              hasMore: response.pages > 1,
              currentPage: 1,
            });

            const firstMessage = response.items[0];
            if (firstMessage?.additionalData?.jobSummary?.jobId) {
              try {
                const jobId = firstMessage.additionalData.jobSummary.jobId;
                const jobResponse = await fetchJob({
                  jobId: Number(jobId),
                  client,
                });
                const jobStatus = jobResponse.status;
                const isConfirmed = jobStatus === 'user_accepted';
                set({ jobSummaryConfirmed: isConfirmed });
              } catch (error) {
                console.error('Failed to check job status:', error);
                set({ jobSummaryConfirmed: false });
              }
            } else {
              set({ jobSummaryConfirmed: false });
            }
          }

          return { isValid, hasMessages };
        } catch (error) {
          console.error('Failed to fetch messages:', error);
          return { isValid: false, hasMessages: false };
        } finally {
          set({ isLoading: false });
        }
      },

      loadMoreMessages: async (chatId, client) => {
        const { currentPage, hasMore, isLoading } = get();
        if (!hasMore || isLoading) return;

        set({ isLoading: true });
        try {
          const response = await fetchMessages({
            chatId,
            page: currentPage + 1,
            client,
          });

          const isValid = response && Array.isArray(response.items);
          const hasMessages = isValid && response.items.length > 0;

          if (isValid && hasMessages) {
            useChats.getState().appendMessages(response.items, chatId);
            set({
              hasMore: response.page < response.pages,
              currentPage: currentPage + 1,
            });
          } else {
            console.error('Invalid response when loading more messages');
            set({ hasMore: false });
          }
        } catch (error) {
          console.error('Failed to load more messages:', error);
          set({ hasMore: false });
        } finally {
          set({ isLoading: false });
        }
      },

      setJobSummaryConfirmed: (confirmed: boolean) => {
        set({ jobSummaryConfirmed: confirmed });
      },

      setShowItLooksGoodMessage: (show: boolean) => {
        set({ showItLooksGoodMessage: show });
      },

      setUserDetailsValidated: (validated: boolean) => {
        set({ userDetailsValidated: validated });
      },

      resetStore: () => {
        set({
          isLoading: false,
          hasMore: true,
          currentPage: 1,
          jobSummaryConfirmed: false,
          showItLooksGoodMessage: false,
          userDetailsValidated: false,
        });
      },
    }),
    {
      name: 'messages-storage',
      partialize: (state) => ({
        jobSummaryConfirmed: state.jobSummaryConfirmed,
        showItLooksGoodMessage: state.showItLooksGoodMessage,
        userDetailsValidated: state.userDetailsValidated,
      }),
    }
  )
);
