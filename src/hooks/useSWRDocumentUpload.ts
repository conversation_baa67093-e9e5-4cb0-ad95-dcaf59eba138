import useSWRMutation from 'swr/mutation';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { useCallback } from 'react';
import { Context } from '@/api/notifications';
import { BackendClient } from '@/api/BackendClient';
import { useFiles } from '@/containers/FilesPage/useFiles';

export interface UploadError {
  message: string;
  status?: number;
  details?: string;
}

export function useSWRDocumentUpload(uploadContext: Context) {
  const { uploadFiles } = useFiles({
    getUploadContext: uploadContext,
    setUploadContext: uploadContext
  });

  const uploadFetcher = useCallback(
    async (url: string, { arg }: { arg: { formData: FormData } }) => {
      const { formData } = arg;

      const file = formData.get('file') as File;
      if (!file) {
        throw new Error('No file provided');
      }

      return await uploadFiles([file]);
    },
    [uploadFiles]
  );

  const { trigger, isMutating, error } = useSWRMutation(
    getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/`),
    uploadFetcher
  );

  return {
    uploadDocument: trigger,
    isUploading: isMutating,
    error: error as UploadError | null,
  };
}
