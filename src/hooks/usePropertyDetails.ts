import { create } from 'zustand';
import {
  createProperty,
  mapRoleToUserPropertyRelationType,
  Property,
  PropertyCreate,
  PropertyUpdate,
  updateProperty,
} from '@/api/properties';
import { AddressType } from '@/components/AddressInput/AddressInput.types';
import { BackendClient } from '@/api/BackendClient';
import useBackendClient from '@/hooks/useBackendClient';

export interface PropertyDetailsData {
  address: AddressType;
  role: string;
  propertyType?: string;
  ownershipType?: string;
}

interface PropertyDetailsState {
  property: Property | null;
  isLoading: boolean;
  isCreating: boolean;
  isSaving: boolean;
  error: string | null;

  createPropertyFromDetails: (
    data: PropertyDetailsData,
    client: BackendClient
  ) => Promise<Property>;
  updatePropertyFromDetails: (
    propertyId: number,
    data: PropertyDetailsData,
    client: BackendClient
  ) => Promise<Property>;
  reset: () => void;
}

const toCreateRequest = (data: PropertyDetailsData): PropertyCreate => {
  const userPropertyRelationshipType = data.role
    ? mapRoleToUserPropertyRelationType(data.role)
    : undefined;
  if ('id' in data.address) {
    return {
      idealPostcodesAddressId: data.address.id,
      userPropertyRelationshipType,
    };
  } else {
    return {
      manualAddress: {
        streetLine1: data.address.line1 || '',
        streetLine2: data.address.line2 || null,
        townOrCity: data.address.city || '',
        postcode: data.address.postcode || '',
      },
      userPropertyRelationshipType,
    };
  }
};

const toUpdateRequest = (data: PropertyDetailsData): PropertyUpdate => {
  const userPropertyRelationshipType = data.role
    ? mapRoleToUserPropertyRelationType(data.role)
    : undefined;
  if ('id' in data.address) {
    return {
      idealPostcodesAddressId: data.address.id,
      userPropertyRelationshipType,
    };
  } else {
    return {
      manualAddress: data.address
        ? {
            streetLine1: data.address.line1 || '',
            streetLine2: data.address.line2 || null,
            townOrCity: data.address.city || '',
            postcode: data.address.postcode || '',
          }
        : undefined,
      userPropertyRelationshipType,
    };
  }
};

export const usePropertyDetails = create<PropertyDetailsState>((set) => ({
  property: null,
  isLoading: false,
  isCreating: false,
  isSaving: false,
  error: null,

  createPropertyFromDetails: async (data: PropertyDetailsData, client: BackendClient) => {
    set({ isCreating: true, error: null });

    try {
      const createdProperty = await createProperty({ request: toCreateRequest(data), client });
      set({ property: createdProperty, isCreating: false });
      return createdProperty;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to create property',
        isCreating: false,
      });
      throw error;
    }
  },

  updatePropertyFromDetails: async (
    propertyId: number,
    data: PropertyDetailsData,
    client: BackendClient
  ) => {
    set({ isCreating: true, error: null });

    try {
      const updatedProperty = await updateProperty({
        propertyId,
        request: toUpdateRequest(data),
        client,
      });

      set({ property: updatedProperty, isCreating: false });
      return updatedProperty;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update property',
        isCreating: false,
      });
      throw error;
    }
  },

  reset: () => {
    set({
      property: null,
      isLoading: false,
      isCreating: false,
      isSaving: false,
      error: null,
    });
  },
}));

export const usePropertyDetailsActions = () => {
  const { client } = useBackendClient();
  const { createPropertyFromDetails, updatePropertyFromDetails, reset } = usePropertyDetails();

  const handleCreateProperty = async (data: PropertyDetailsData) => {
    return await createPropertyFromDetails(data, client);
  };

  const handleUpdateProperty = async (propertyId: number, data: PropertyDetailsData) => {
    return await updatePropertyFromDetails(propertyId, data, client);
  };

  return {
    createProperty: handleCreateProperty,
    updateProperty: handleUpdateProperty,
    reset,
  };
};
