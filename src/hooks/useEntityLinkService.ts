import {
  createEntityLink,
  deleteEntityLink,
  getEntityLink,
  ICreateEntityLinkDto,
  IGetEntityLinkDto,
} from '@/api/entityLink';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import useBackendClient from '@/hooks/useBackendClient';

export const useCreateEntityLink = () => {
  const { client } = useBackendClient();

  const createEntityLinkWithToken = async (
    _key: string,
    { arg }: { arg: { linkingEntity: ICreateEntityLinkDto } }
  ) => {
    const url = getApiUrl(`${API_ENDPOINTS.ENTITY_LINKS}`);

    return createEntityLink(url, { arg: { client, linkingEntity: arg.linkingEntity } });
  };

  return useSWRMutation('createLink', createEntityLinkWithToken);
};

export const useGetEntityLink = (linkingEntity: IGetEntityLinkDto) => {
  const { client } = useBackendClient();

  const getEntityLinkWithToken = async (url: string) => {
    return getEntityLink(url, { arg: { client, linkingEntity } });
  };

  return useSWR(
    linkingEntity.entityId >= 0 ? getApiUrl(`${API_ENDPOINTS.ENTITY_LINKS}`) : null,
    getEntityLinkWithToken,
    {
      refreshInterval: (data) => {
        return data && data?.length > 0 ? 0 : 2000;
      },
      refreshWhenHidden: false,
      refreshWhenOffline: false,
    }
  );
};

export const useDeleteLink = () => {
  const { client } = useBackendClient();

  const deleteEntityLinkWithToken = async (
    _: string,
    {
      arg,
    }: {
      arg: {
        entityTypeA: string;
        entityIdA: string;
        entityTypeB: string;
        entityIdB: string;
      };
    }
  ) => {
    const url = getApiUrl(
      `${API_ENDPOINTS.ENTITY_LINKS}/${arg.entityTypeA}/${arg.entityIdA}/${arg.entityTypeB}/${arg.entityIdB}`
    );
    return deleteEntityLink(url, { arg: { client } });
  };

  return useSWRMutation('deleteLink', deleteEntityLinkWithToken);
};
