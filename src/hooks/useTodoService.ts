import { fetchTodos, updateTodo } from '@/api/todos/todos';
import { ITodoDto, IUpdateTodoDto } from '@/api/todos/types';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import useBackendClient from '@/hooks/useBackendClient';

export const useUpdateTodo = () => {
  const { client } = useBackendClient();

  const updateTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id']; data: IUpdateTodoDto } }
  ) => {
    const url = getApiUrl(`${API_ENDPOINTS.TODOS}/${arg.todoId}`);
    return updateTodo(url, { arg: { client, updateTodo: arg.data } });
  };

  return useSWRMutation('updateTodo', updateTodoWithGetToken);
};

export const useGetTodos = (enabled = true) => {
  const { client } = useBackendClient();

  const fetchTodosWithGetToken = async (url: string) => {
    return fetchTodos(url, client);
  };

  return useSWR(
    enabled ? `${getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED)}` : null,
    fetchTodosWithGetToken
  );
};

export const useGetTodosSuggested = ({ page = 1, size = 5, enabled = true }) => {
  const { client } = useBackendClient();

  const fetchTodosSuggestedWithGetToken = async (url: string) => {
    return fetchTodos(url, client);
  };

  return useSWR(
    enabled ? getApiUrl(`${API_ENDPOINTS.TODOS_SUGGESTED}?page=${page}&size=${size}`) : null,
    fetchTodosSuggestedWithGetToken
  );
};

export const useDeleteTodo = () => {
  const { client } = useBackendClient();

  const deleteTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id'] } }
  ) => {
    const url = getApiUrl(`${API_ENDPOINTS.TODOS}/${arg.todoId}`);
    return client.delete<void>(url);
  };

  return useSWRMutation('deleteTodo', deleteTodoWithGetToken);
};

export const useAcceptTodo = () => {
  const { client } = useBackendClient();

  const acceptTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id'] } }
  ) => {
    const url = getApiUrl(`${API_ENDPOINTS.TODO_ACCEPT.replace('{todoId}', String(arg.todoId))}`);
    return client.post<void>(url);
  };

  return useSWRMutation('acceptTodo', acceptTodoWithGetToken);
};

export const useRejectTodo = () => {
  const { client } = useBackendClient();

  const acceptTodoWithGetToken = async (
    _key: string,
    { arg }: { arg: { todoId: ITodoDto['id'] } }
  ) => {
    const url = getApiUrl(`${API_ENDPOINTS.TODO_REJECT.replace('{todoId}', String(arg.todoId))}`);
    return client.post<void>(url);
  };

  return useSWRMutation('acceptTodo', acceptTodoWithGetToken);
};
