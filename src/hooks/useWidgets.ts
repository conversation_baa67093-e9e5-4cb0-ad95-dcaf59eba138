import { create } from 'zustand';
import {
  fetchProperties,
  Property,
  PropertiesResponse,
  findAddress,
  saveAddress,
  saveAccessInstruction,
} from '@/api/properties';
import { BackendClient } from '@/api/BackendClient';
import { AddressType } from '@/components/AddressInput';

export interface Address {
  id: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postcode: string;
  country: string;
}

// const formattedAddress = `${streetLine1}${streetLine2 ? ', ' + streetLine2 : ''}, ${townOrCity}, ${postcode}`;
const getFormattedAddress = (address: Property['address']) => {
  if (!address) {
    return '-';
  }
  const { streetLine1, streetLine2, townOrCity, postcode } = address;
  return `${streetLine1}${streetLine2 ? ', ' + streetLine2 : ''}, ${townOrCity}, ${postcode}`;
};

export interface AddressFields {
  line1: string;
  line2?: string;
  city: string;
  postcode: string;
}

interface WidgetsState {
  addresses: Address[];
  properties: Property[];
  hasAddress: boolean;
  addressData: AddressFields | null;
  accessInstruction: string;
  propertiesResponse: PropertiesResponse | null;
  addressValue: string | null;

  isLoadingProperties: boolean;
  isLoadingAddresses: boolean;
  isSavingAddress: boolean;

  fetchProperties: (client: BackendClient) => Promise<void>;
  findAddresses: (query: string, client: BackendClient) => Promise<object>;
  saveAddress: (addressValue: AddressType, client: BackendClient) => Promise<boolean>;

  setAddressData: (data: AddressFields) => void;
  setAccessInstruction: (instruction: string) => void;
  saveAccessInstruction: (instruction: string, client: BackendClient) => Promise<void>;
  resetStore: () => void;
}

export const useWidgets = create<WidgetsState>((set, get) => ({
  addresses: [],
  properties: [],
  hasAddress: true,
  addressData: null,
  accessInstruction: '',
  propertiesResponse: null,
  addressValue: null,
  isLoadingProperties: false,
  isLoadingAddresses: false,
  isSavingAddress: false,

  fetchProperties: async (client: BackendClient) => {
    set({ isLoadingProperties: true });
    try {
      const response = await fetchProperties({ client });

      if (response.items && response.items.length === 0) {
        set({
          hasAddress: false,
          properties: response.items,
          propertiesResponse: response,
        });
      } else {
        set({
          hasAddress: true,
          properties: response.items,
          propertiesResponse: response,
          accessInstruction:
            (response.items.length > 0 && response.items[0].address?.houseAccess) || '',
          addressValue: response.items[0].address
            ? getFormattedAddress(response.items[0].address)
            : null,
        });
      }
    } catch (error) {
      console.error('Error fetching properties:', error);
      set({ hasAddress: false });
    } finally {
      set({ isLoadingProperties: false });
    }
  },

  findAddresses: async (query: string, client: BackendClient) => {
    set({ isLoadingAddresses: true });
    try {
      const addresses = await findAddress({ query, client });
      set({ addresses });
      return addresses as object;
    } catch (error) {
      console.error('Error finding addresses:', error);
      return [] as object;
    } finally {
      set({ isLoadingAddresses: false });
    }
  },

  saveAddress: async (addressValue: AddressType, client: BackendClient) => {
    set({ isSavingAddress: true });
    try {
      let response: Property;

      if ('id' in addressValue) {
        response = await saveAddress({
          request: { idealPostcodesAddressId: addressValue.id },
          client,
        });
      } else {
        response = await saveAddress({
          request: {
            manualAddress: {
              streetLine1: addressValue.line1,
              streetLine2: addressValue.line2 || '',
              townOrCity: addressValue.city,
              postcode: addressValue.postcode,
            },
          },
          client,
        });
      }

      set((state) => ({
        hasAddress: true,
        properties: [...state.properties, response],
        addressValue: getFormattedAddress(response.address),
      }));

      return true;
    } catch (error) {
      console.error('Error saving address:', error);
    } finally {
      set({ isSavingAddress: false });
    }
    return false;
  },

  setAddressData: (data: AddressFields) => set({ addressData: data }),
  setAccessInstruction: (instruction: string) => set({ accessInstruction: instruction }),
  saveAccessInstruction: async (instruction: string, client: BackendClient) => {
    set({ accessInstruction: instruction });
    const { properties } = get();
    const propertyId = properties[0].id;
    await saveAccessInstruction({
      request: {
        propertyId,
        address: {
          houseAccess: instruction,
        },
      },
      client,
    });
  },

  resetStore: () => {
    set({
      addresses: [],
      properties: [],
      hasAddress: true,
      addressData: null,
      accessInstruction: '',
      propertiesResponse: null,
      addressValue: null,
      isLoadingProperties: false,
      isLoadingAddresses: false,
      isSavingAddress: false,
    });
  },
}));
