import { useAuth } from '@clerk/nextjs';
import { useMemo } from 'react';
import { BackendClient } from '@/api/BackendClient';
import { create } from 'zustand';
import { useUniversalAuth } from '@/hooks/useUniversalAuth';

type BackendClientStore = {
  isForbidden: boolean;
  isQuotaLimitReached: boolean;
  setForbidden: (value: boolean) => void;
  setQuotaLimitReached: (value: boolean) => void;
};

const useBackendClientStore = create<BackendClientStore>((set) => ({
  isForbidden: false,
  isQuotaLimitReached: false,
  setForbidden: (value: boolean) => set({ isForbidden: value }),
  setQuotaLimitReached: (value: boolean) => set({ isQuotaLimitReached: value }),
}));

export default function useBackendClient() {
  const { getToken } = useUniversalAuth();
  const { setForbidden, setQuotaLimitReached, isForbidden, isQuotaLimitReached } = useBackendClientStore();

  const client = useMemo(
    () =>
      new BackendClient({
        getToken,
        onQuotaLimitReached: () => setQuotaLimitReached(true),
        onForbidden: () => setForbidden(true)
      }),
    [getToken, setForbidden, setQuotaLimitReached]
  );

  return {
    client,
    isForbidden,
    isQuotaLimitReached,
  };
}
