import { beforeEach, describe, expect, it } from 'vitest';
import { act, renderHook } from '@testing-library/react';
import { useChats } from '../useChats';
import { Message, MessageTypeValue, SenderType } from '@/types/messages';
import { DocumentDto } from '@/api/documents';

type DummyMessage = Message & { documents: DocumentDto[] };

const makeDocument = (overrides?: Partial<DocumentDto>): DocumentDto =>
  ({
    id: 1,
    fileName: 'file.txt',
    sizeInKiloBytes: 1,
    browserMimeType: 'text/plain',
    createdAt: new Date().toISOString(),
    uploadContext: 'unit-test',
    status: 'processing',
    ...overrides,
  }) as unknown as DocumentDto;

const makeMessage = (docs: DocumentDto[], overrides?: Partial<DummyMessage>): DummyMessage =>
  ({
    id: 10,
    content: 'hello',
    senderType: 'user' as SenderType,
    timestamp: new Date().toISOString(),
    type: MessageTypeValue.Text,
    documents: docs,
    ...overrides,
  }) as DummyMessage;

const makeChat = (messages: DummyMessage[], overrides?: Partial<Chat>): Chat => ({
  id: 100,
  title: 'test',
  status: 'active',
  messages,
  ...overrides,
});

describe('useChats - updateDocumentsInChats', () => {
  beforeEach(() => {
    act(() => {
      useChats.getState().resetStore();
    });
  });

  it('updates chats when matching documents are provided', () => {
    const initialDoc = makeDocument({ status: 'processing' });
    const updatedDoc = makeDocument({ status: 'processingCompleted' });

    const chat = makeChat([makeMessage([initialDoc])]);

    act(() => {
      useChats.setState({ chats: [chat] });
    });

    const { result } = renderHook(() => useChats());

    const originalChatsRef = result.current.chats;

    act(() => {
      result.current.updateDocumentsInChats([updatedDoc]);
    });

    const newChatsRef = result.current.chats;

    expect(newChatsRef).not.toBe(originalChatsRef);
    const docInStore = newChatsRef[0].messages![0].documents![0];
    expect(docInStore.status).toBe('processingCompleted');
  });

  it('does NOT change chat objects when no document matches', () => {
    const initialDoc = makeDocument({ id: 1 });
    const nonMatchingDto = makeDocument({ id: 999 }); // different ID

    const chat = makeChat([makeMessage([initialDoc])]);

    act(() => {
      useChats.setState({ chats: [chat] });
    });

    const { result } = renderHook(() => useChats());

    const originalChatObjRef = result.current.chats[0];

    act(() => {
      result.current.updateDocumentsInChats([nonMatchingDto]);
    });

    expect(result.current.chats[0]).toBe(originalChatObjRef);
  });

  it('does nothing (keeps chat object reference) when dtos array is empty', () => {
    const doc = makeDocument();

    const chat = makeChat([makeMessage([doc])]);

    act(() => {
      useChats.setState({ chats: [chat] });
    });

    const { result } = renderHook(() => useChats());

    const originalChatObjRef = result.current.chats[0];

    act(() => {
      result.current.updateDocumentsInChats([]);
    });

    expect(result.current.chats[0]).toBe(originalChatObjRef);
  });

  it('handles mixed scenario with multiple chats/messages', () => {
    /**
     * Chat-A: one message – its document WILL be updated
     * Chat-B: two messages – NONE of its documents should change
     */
    const docA1_old = makeDocument({ id: 1, status: 'processing' });
    const docA1_new = makeDocument({ id: 1, status: 'processingCompleted' });

    const docB1 = makeDocument({ id: 101 });
    const docB2 = makeDocument({ id: 102 });

    const chatA = makeChat([makeMessage([docA1_old])], { id: 1, title: 'A' });
    const chatB = makeChat(
      [makeMessage([docB1], { id: 11 }), makeMessage([docB2], { id: 12 })],
      { id: 2, title: 'B' },
    );

    act(() => {
      useChats.setState({ chats: [chatA, chatB] });
    });

    const { result } = renderHook(() => useChats());

    // Keep references for later identity checks
    const origChatARef = result.current.chats.find((c) => c.id === 1)!;
    const origChatBRef = result.current.chats.find((c) => c.id === 2)!;

    // --- act: update only docA1 ---
    act(() => {
      result.current.updateDocumentsInChats([docA1_new]);
    });

    const newChatARef = result.current.chats.find((c) => c.id === 1)!;
    const newChatBRef = result.current.chats.find((c) => c.id === 2)!;

    // Chat-A changed (new reference) and its document got new status
    expect(newChatARef).not.toBe(origChatARef);
    expect(newChatARef.messages![0].documents![0].status).toBe('processingCompleted');

    // Chat-B stayed exactly the same (same reference, so no re-render)
    expect(newChatBRef).toBe(origChatBRef);
  });


});