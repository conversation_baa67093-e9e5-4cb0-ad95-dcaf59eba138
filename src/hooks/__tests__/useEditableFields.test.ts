import { renderHook, act } from '@testing-library/react';
import { vi } from 'vitest';
import { useEditableFields } from '../useEditableFields';

describe('useEditableFields', () => {
  const mockFields = [
    {
      id: '1',
      label: 'Brand',
      value: '',
      editable: true,
      type: 'text',
    },
    {
      id: '2',
      label: 'Model',
      value: '',
      editable: true,
      type: 'text',
    },
  ];

  it('should allow saving when only title is filled (other fields empty)', () => {
    const mockOnSave = vi.fn();
    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: mockFields,
        initialTitle: 'Boiler',
        onSave: mockOnSave,
      })
    );

    expect(result.current.title).toBe('Boiler');
    expect(result.current.isAllFieldsFilled).toBe(true); // Should be true because only title is required
    expect(result.current.hasEmptyFields).toBe(true); // Should be true because fields are empty

    act(() => {
      result.current.handleSave();
    });

    expect(mockOnSave).toHaveBeenCalledWith(mockFields, 'Boiler');
  });

  it('should not allow saving when title is empty', () => {
    const mockOnSave = vi.fn();
    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: mockFields,
        initialTitle: '',
        onSave: mockOnSave,
      })
    );

    expect(result.current.isAllFieldsFilled).toBe(false); // Should be false because title is empty

    act(() => {
      result.current.handleSave();
    });

    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('should allow saving when title is filled and some fields have values', () => {
    const fieldsWithValues = [
      {
        id: '1',
        label: 'Brand',
        value: 'Bosch',
        editable: true,
        type: 'text',
      },
      {
        id: '2',
        label: 'Model',
        value: '', // Empty field
        editable: true,
        type: 'text',
      },
    ];

    const mockOnSave = vi.fn();
    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: fieldsWithValues,
        initialTitle: 'Boiler',
        onSave: mockOnSave,
      })
    );

    expect(result.current.isAllFieldsFilled).toBe(true); // Should be true because title is filled
    expect(result.current.hasEmptyFields).toBe(true); // Should be true because one field is empty

    act(() => {
      result.current.handleSave();
    });

    expect(mockOnSave).toHaveBeenCalledWith(fieldsWithValues, 'Boiler');
  });

  it('should update title and maintain validation state', () => {
    const mockOnSave = vi.fn();
    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: mockFields,
        initialTitle: '',
        onSave: mockOnSave,
      })
    );

    expect(result.current.isAllFieldsFilled).toBe(false);

    act(() => {
      result.current.handleTitleChange('New Appliance');
    });

    expect(result.current.title).toBe('New Appliance');
    expect(result.current.isAllFieldsFilled).toBe(true);

    act(() => {
      result.current.handleSave();
    });

    expect(mockOnSave).toHaveBeenCalledWith(mockFields, 'New Appliance');
  });
});

describe('Save button validation', () => {
  it('should enable save when initially filled field becomes empty (allowing field reset)', () => {
    const fieldsWithData = [
      {
        id: '1',
        label: 'Brand',
        value: 'Samsung',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '2',
        label: 'Model',
        value: 'RF23M8070SR',
        editable: true,
        type: 'text' as const,
      },
    ];

    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: fieldsWithData,
        initialTitle: 'Fridge',
      })
    );

    expect(result.current.isAllFieldsFilled).toBe(true);

    act(() => {
      result.current.handleFieldChange('1', '');
    });

    expect(result.current.isAllFieldsFilled).toBe(true);
  });

  it('should disable save when no changes are made during editing', () => {
    const fieldsWithData = [
      {
        id: '1',
        label: 'Brand',
        value: 'Samsung',
        editable: true,
        type: 'text' as const,
      },
    ];

    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: fieldsWithData,
        initialTitle: 'Fridge',
        isEditingProp: true,
      })
    );

    expect(result.current.isAllFieldsFilled).toBe(false);

    act(() => {
      result.current.handleFieldChange('1', 'Samsung');
    });

    expect(result.current.isAllFieldsFilled).toBe(false);
  });

  it('should enable save when changes are made', () => {
    const fieldsWithData = [
      {
        id: '1',
        label: 'Brand',
        value: 'Samsung',
        editable: true,
        type: 'text' as const,
      },
    ];

    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: fieldsWithData,
        initialTitle: 'Fridge',
        isEditingProp: true,
      })
    );

    act(() => {
      result.current.handleFieldChange('1', 'LG');
    });

    expect(result.current.isAllFieldsFilled).toBe(true);
  });

  it('should enable save when title is changed', () => {
    const fieldsWithData = [
      {
        id: '1',
        label: 'Brand',
        value: 'Samsung',
        editable: true,
        type: 'text' as const,
      },
    ];

    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: fieldsWithData,
        initialTitle: 'Fridge',
        isEditingProp: true,
      })
    );

    act(() => {
      result.current.handleTitleChange('Refrigerator');
    });

    expect(result.current.isAllFieldsFilled).toBe(true);
  });

  it('should allow save for new items with just title filled', () => {
    const emptyFields = [
      {
        id: '1',
        label: 'Brand',
        value: '',
        editable: true,
        type: 'text' as const,
      },
    ];

    const { result } = renderHook(() =>
      useEditableFields({
        initialFields: emptyFields,
        initialTitle: '',
      })
    );

    act(() => {
      result.current.handleTitleChange('New Appliance');
    });

    expect(result.current.isAllFieldsFilled).toBe(true);
  });
});
