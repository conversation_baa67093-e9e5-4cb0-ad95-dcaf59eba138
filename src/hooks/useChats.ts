import { create } from 'zustand';
import { fetchChats, sendMessage } from '@/api/chats';
import {
  CompleteSendMessageResponse,
  Message,
  MessageTypeValue,
  SendMessageResponse,
} from '@/types/messages';
import { Chat } from '@/types/chats';
import { EntityType } from '@/api/entityLink';
import { BackendClient } from '@/api/BackendClient';
import { UploadedFile } from '@/types/file';
import { DocumentDto } from '@/api/documents';
import { mapFetchedFilesToDocuments } from '@/utils/messageUtils';
import { isEqual } from 'lodash';

interface OptimisticMessage {
  content: string;
  attachments?: UploadedFile[];
}

function titleFromContent(content: string) {
  const sliced = content.trim().slice(0, 50);
  return sliced.length > 0 ? sliced : 'Inquiry';
}

interface ChatState {
  chats: Chat[];
  isLoading: boolean;
  hasMore: boolean;
  currentPage: number;
  optimisticMessage: OptimisticMessage | null;
  jobSummaryConfirmed: boolean;
  isRetryButtonShown: boolean;
  isStreamingMessage: boolean;
  loadMoreChats: (client: BackendClient) => Promise<void>;
  sendMessage: (
    chatId: number | undefined,
    content: string,
    client: BackendClient,
    attachments?: UploadedFile[],
    onChatId?: (chatId: number) => void,
    linked_entity?: {
      entityType: EntityType | null;
      id: number | null;
    }
  ) => Promise<void>;
  appendMessages: (messages: Message[], chatId: number) => void;
  replaceMessages: (messages: Message[], chatId: number) => void;
  updateDocumentsInChats: (dtos: DocumentDto[]) => void;
  setJobSummaryConfirmed: (confirmed: boolean) => void;
  setIsStreamingMessage: (streaming: boolean) => void;
  resetStore: () => void;
}

function mergeMessages(
  current: Message[],
  systemResponse: CompleteSendMessageResponse,
  userMessage: { content: string; attachments: UploadedFile[] }
): Message[] {
  const documentsFromAttachments = mapFetchedFilesToDocuments(userMessage.attachments);

  return [
    {
      content: systemResponse.message.content,
      id: systemResponse.systemMessageId,
      senderType: 'system',
      timestamp: new Date().toISOString(),
      type: MessageTypeValue.Text,
      additionalData: systemResponse.additionalData,
    },
    {
      id: systemResponse.userMessageId,
      content: userMessage.content,
      documents: documentsFromAttachments,
      type: MessageTypeValue.Text,
      timestamp: new Date().toISOString(),
      senderType: 'user',
    },
    ...current.filter(
      (m) => m.id !== systemResponse.systemMessageId && m.id !== systemResponse.userMessageId
    ),
  ];
}

function chatWithUpdatedDocuments(chat: Chat, documents: DocumentDto[]): Chat {
  if (!chat?.messages?.length || !documents?.length) {
    return chat;
  }

  const docsById = new Map<number, DocumentDto>(
    documents.map((d) => [d.id, d]),
  );

  let isAnyDocChanged = false;
  const updatedMessages = chat.messages.map((msg) => {
    if (!msg.documents?.length) {
      return msg;
    }

    let areDocsSame = true;
    const newDocs = msg.documents.map((doc) => {
      const updated = docsById.get(doc.id);
      if (!updated) {
        return doc;
      }

      if (!isEqual(updated, doc)) {
        areDocsSame = false;
        isAnyDocChanged = true;
        return updated;
      }
      return doc;
    });

    return areDocsSame ? msg : { ...msg, documents: newDocs };
  });

  if (!isAnyDocChanged) {
    return chat;
  }

  return { ...chat, messages: updatedMessages };
}

export const useChats = create<ChatState>((set, get) => ({
  chats: [],
  isLoading: false,
  hasMore: true,
  currentPage: 0,
  optimisticMessage: null,
  jobSummaryConfirmed: false,
  isRetryButtonShown: false,
  isStreamingMessage: false,

  setJobSummaryConfirmed: (confirmed: boolean) => {
    set({ jobSummaryConfirmed: confirmed });
  },

  loadMoreChats: async (client: BackendClient) => {
    const { currentPage, hasMore, isLoading } = get();
    if (!hasMore || isLoading) return;

    set({ isLoading: true });
    try {
      const response = await fetchChats({
        client,
        page: currentPage + 1,
      });

      set((state) => ({
        chats: [
          ...state.chats.filter((c) => !response.items.some((i) => i.id === c.id)),
          ...response.items,
        ],
        hasMore: response.page < response.pages,
        currentPage: currentPage + 1,
      }));
    } catch (error) {
      console.error('Failed to load more chats:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  appendMessages: (messages, chatId) => {
    set((state) => ({
      chats: state.chats.map((chat) =>
        chat.id === chatId
          ? {
              ...chat,
              messages: [...(chat.messages || []), ...messages],
            }
          : chat
      ),
    }));
  },

  replaceMessages: (messages, chatId) => {
    set((state) => ({
      chats: state.chats.some((chat) => chat.id === chatId)
        ? state.chats.map((chat) =>
            chat.id === chatId
              ? {
                  ...chat,
                  messages,
                }
              : chat
          )
        : [
            ...state.chats,
            {
              id: chatId,
              messages,
              title: '',
              status: '',
            },
          ],
    }));
  },

  sendMessage: async (
    chatId: number | undefined,
    content: string,
    client: BackendClient,
    attachments: UploadedFile[] = [],
    onChatId?: (chatId: number) => void,
    linked_entity?: {
      entityType: EntityType | null;
      id: number | null;
    }
  ) => {
    try {
      set({
        isStreamingMessage: true,
        isRetryButtonShown: false,
        optimisticMessage: chatId
          ? {
              content,
              attachments,
            }
          : undefined,
      });

      await sendMessage({
        chatId,
        message: {
          content,
          type: 'text',
          additionalData: {
            device: navigator.userAgent,
            location: window.location.href,
          },
        },
        attachments: attachments.map((attachment) => ({
          documentId: attachment.documentId,
        })),
        linked_entity,
        client,
        onStreamStart: () => {
          set(() => ({
            isStreamingMessage: true,
          }));
        },
        onStreamEnd: () => {
          set(() => ({ isStreamingMessage: false }));
        },
        onError: () => {
          set(() => ({ isRetryButtonShown: true, isStreamingMessage: false }));
        },
        onContent: () => {
          set(() => ({ optimisticMessage: null }));
        },
        onSendMessageResponse: async (response: SendMessageResponse) => {
          const chatId = response.chatId;
          onChatId && onChatId(chatId);

          if (!get().chats.find((c) => c.id === chatId)) {
            set((state) => ({
              chats: [
                {
                  id: chatId,
                  title: titleFromContent(content),
                  messages: [],
                  status: 'active',
                },
                ...state.chats,
              ],
            }));
          }
        },
        onComplete: async (res) => {
          const chatId = res.chatId;
          set((state) => ({
            chats: state.chats.some((c) => c.id === chatId)
              ? state.chats.map((c) =>
                  c.id === chatId
                    ? {
                        ...c,
                        messages: mergeMessages(c.messages || [], res, {
                          content,
                          attachments,
                        }),
                      }
                    : c
                )
              : [
                  ...state.chats,
                  {
                    id: chatId,
                    messages: mergeMessages([], res, {
                      content,
                      attachments,
                    }),
                    title: titleFromContent(content),
                    status: '',
                  },
                ],
          }));
        },
      });
    } catch (error) {
      console.error('Failed to send message:', error);
      set({ isRetryButtonShown: true });
    }
  },

  setIsStreamingMessage: (isStreamingMessage: boolean) => {
    set({ isStreamingMessage });
  },

  updateDocumentsInChats: (dtos: DocumentDto[]) => {
    set((state) => ({
      chats: state.chats.map((chat) => chatWithUpdatedDocuments(chat, dtos)),
    }));
  },

  resetStore: () => {
    set({
      chats: [],
      isLoading: false,
      hasMore: true,
      currentPage: 1,
      optimisticMessage: null,
      jobSummaryConfirmed: false,
      isRetryButtonShown: false,
      isStreamingMessage: false,
    });
  },
}));
