import axios from 'axios';
import { useAuth } from '@clerk/nextjs';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';
import { AddressFields } from '@/components/AddressInput/AddressInput.types';
import { PropertyTenureType, PropertyType, UserPropertyRelationType } from '@/api/properties';
import useBackendClient from '@/hooks/useBackendClient';

interface OnboardingData {
  selectedAddress: string | AddressFields;
  selectedRole: UserPropertyRelationType;
  selectedPropertyType: PropertyType;
  selectedOwnershipType: PropertyTenureType;
}

interface DemoRequestData {
  companyName: string;
  businessEmail: string;
  numberOfPropertiesManaged: string;
}

export const useSendOnboardingData = () => {
  const { client } = useBackendClient();

  const sendOnboardingData = async ({
    selectedAddress,
    selectedRole,
    selectedPropertyType,
    selectedOwnershipType,
  }: OnboardingData) => {
    let requestData;

    if (typeof selectedAddress === 'string') {
      requestData = {
        idealPostcodesAddressId: selectedAddress,
        type: selectedPropertyType.toString(),
        tenureType: selectedOwnershipType.toString(),
        userPropertyRelationshipType: selectedRole.toString(),
      };
    } else {
      requestData = {
        manualAddress: {
          streetLine1: selectedAddress.line1 || '',
          streetLine2: selectedAddress.line2 || '',
          townOrCity: selectedAddress.city || '',
          postcode: selectedAddress.postcode || '',
        },
        type: selectedPropertyType.toString(),
        tenureType: selectedOwnershipType.toString(),
        userPropertyRelationshipType: selectedRole.toString(),
      };
    }

    const addressSendRequest = client.post(getApiUrl(`${API_ENDPOINTS.PROPERTIES}/`), requestData);

    return {
      addressSendRequest,
    };
  };

  const sendDemoRequest = async ({
    companyName,
    businessEmail,
    numberOfPropertiesManaged,
  }: DemoRequestData) => {
    const demoSendRequest = client.post(getApiUrl('user/request-b2b-demo/'), {
      companyName,
      businessEmail,
      numberOfPropertiesManaged,
    });

    return {
      demoSendRequest,
    };
  };

  return { sendOnboardingData, sendDemoRequest };
};
