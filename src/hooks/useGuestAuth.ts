import { useCallback, useEffect } from 'react';
import { useAuthStore } from '@/stores/auth.store';
import { createGuestUser } from '@/api/user';

const guestCreationState = {
  isCreating: false,
  promise: null as Promise<string> | null,
};

export const useGuestAuth = () => {
  const { guestAuth, setGuestAuth, clearGuestAuth, isGuestTokenValid, getValidGuestToken } =
    useAuthStore();

  const createGuest = useCallback(async () => {
    try {
      const response = await createGuestUser();
      setGuestAuth({
        token: response.token,
        userId: response.userId,
        expiresAt: response.expiresAt,
      });
      return response.token;
    } catch (error) {
      throw error;
    }
  }, [setGuestAuth]);

  const ensureGuestAuth = useCallback(async (): Promise<string> => {
    // Check if we have a valid guest token
    const validToken = getValidGuestToken();
    if (validToken) {
      return validToken;
    }

    if (guestCreationState.promise) {
      try {
        return await guestCreationState.promise;
      } catch (error) {
        guestCreationState.promise = null;
        guestCreationState.isCreating = false;
      }
    }

    if (!guestCreationState.isCreating) {
      guestCreationState.isCreating = true;

      const promise = (async () => {
        try {
          clearGuestAuth();
          return await createGuest();
        } catch (error) {
          throw error;
        } finally {
          guestCreationState.promise = null;
          guestCreationState.isCreating = false;
        }
      })();

      guestCreationState.promise = promise;
      return promise;
    } else {
      if (guestCreationState.promise) {
        try {
          return await guestCreationState.promise;
        } catch (error) {
          guestCreationState.promise = null;
          guestCreationState.isCreating = false;
          throw error;
        }
      }
    }

    throw new Error('Failed to create or retrieve guest token');
  }, [getValidGuestToken, clearGuestAuth, createGuest]);

  const clearGuest = useCallback(() => {
    clearGuestAuth();
  }, [clearGuestAuth]);

  const isGuestAuthenticated = useCallback(() => {
    return guestAuth !== null && isGuestTokenValid();
  }, [guestAuth, isGuestTokenValid]);

  useEffect(() => {
    if (guestAuth && !isGuestTokenValid()) {
      clearGuestAuth();
    }
  }, [guestAuth, isGuestTokenValid, clearGuestAuth]);

  return {
    guestAuth,
    createGuest,
    ensureGuestAuth,
    clearGuest,
    isGuestAuthenticated,
    isGuestTokenValid,
    getValidGuestToken,
  };
};
