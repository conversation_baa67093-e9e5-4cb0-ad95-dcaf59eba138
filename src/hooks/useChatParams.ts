import { useParams, useSearchParams } from 'next/navigation';

export type ChatParams = {
  chatId: number | undefined;
  query: string | undefined;
};

export default function useChatParams(): ChatParams {
  const params = useParams();
  const searchParams = useSearchParams();
  const query = searchParams?.get('query');
  const chatId = searchParams?.get('chatId');

  return {
    chatId:
      typeof params?.chatId === 'string'
        ? Number(params.chatId)
        : chatId
          ? Number(chatId)
          : undefined,
    query: typeof query === 'string' ? query : undefined,
  };
}
