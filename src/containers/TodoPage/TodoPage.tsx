'use client';

import { TodoList } from './components/TodoList/TodoList';
import { CreateTodo } from './components/CreateTodo';
import { TodoDetails } from './components/TodoDetails';
import { useTodoPage } from './useTodoPage';
import { TodoConfirmDialog } from './components/TodoConfirmDialog';
import { Typography } from '@/components/Typography/Typography';
import { EmptyState } from './components/EmptyState';
import { parseAsBoolean, useQueryState } from 'nuqs';
import { ApprovalActions } from './components/Todo/actions/ApprovalActions';
import { Spinner } from '@/components/Spinner';
import { cn } from '@/lib/utils';

export const TodoPage = () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setOpen] = useQueryState<boolean>('createTodo', parseAsBoolean.withDefault(false));
  const {
    todos,
    todosSuggested,
    selectedTodoId,
    handleChecked,
    isTodosLoading,
    shouldShowEmptyState,
    handleClickTodo,
    handleCloseTodoDetails,
  } = useTodoPage();

  return (
    <div className="h-full flex flex-col w-full mx-auto px-4 max-w-[798px]">
      <div
        className={cn('flex items-center justify-between py-6 mb-4 flex-shrink-0', {
          'mb-10': !shouldShowEmptyState && todos.length === 0,
        })}
      >
        <Typography font="quasimoda" variant="h4">
          To-Do List
        </Typography>
        <CreateTodo hideTriggerButton={shouldShowEmptyState} />
      </div>

      <div className="flex-1 min-h-0 relative">
        <div className="absolute inset-0 overflow-y-auto pb-16 scrollbar">
          {shouldShowEmptyState && <EmptyState onClick={() => setOpen(true)} />}

          {isTodosLoading && (
            <div className="w-full h-full flex items-center justify-center">
              <Spinner color="var(--colors-yellow-500)" size={100} />
            </div>
          )}

          {todos.length > 0 && (
            <div className="mb-10">
              <TodoList todos={todos} onChecked={handleChecked} onClick={handleClickTodo} />
            </div>
          )}

          {todosSuggested.length > 0 && (
            <>
              <div className="mb-4">
                <Typography font="quasimoda" variant="h4">
                  Suggested by Alfie
                </Typography>
              </div>
              <div className="mb-8">
                <TodoList
                  todos={todosSuggested}
                  onClick={handleClickTodo}
                  ActionsComponent={ApprovalActions}
                />
              </div>
            </>
          )}
        </div>
      </div>

      <TodoDetails
        isOpen={selectedTodoId !== null}
        onClose={handleCloseTodoDetails}
        todoId={selectedTodoId}
      />
      <TodoConfirmDialog />
    </div>
  );
};
