'use client';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useEffect, useMemo, useState } from 'react';
import { TodoDetailsContent } from './TodoDetailsContent';
import { ITodoDto, IUpdateTodoDto } from '@/api/todos/types';
import useSWR, { mutate } from 'swr';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { updateTodo } from '@/api/todos/todos';
import { Button } from '@/components/ui/button';
import { Delete02Icon, PencilEdit01Icon, Tick02Icon } from '@hugeicons-pro/core-stroke-standard';
import * as SolidStandard from '@hugeicons-pro/core-solid-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { EditTodoDetails } from './EditTodoDetails';
import { Spinner } from '@/components/Spinner';
import useSWRMutation from 'swr/mutation';
import { toNaiveISOString } from '@/lib/toNaiveISOString';
import { toast } from '@/components/ui/sonner';
import { useTodoContext } from '../../useTodoContext';
import { useDeleteTodo } from '@/hooks/useTodoService';
import { useGetEntityLink } from '@/hooks/useEntityLinkService';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useChats } from '@/hooks/useChats';
import {
  ApplianceLinkedEntityInfo,
  ChatLinkedEntityInfo,
  ContextualPromptLinkedEntityInfo,
} from '@/api/entityLink';
import useBackendClient from '@/hooks/useBackendClient';

interface TodoDetailsProps {
  isOpen: boolean;
  todoId: ITodoDto['id'] | null;
  onClose?: () => void;
}

export const TodoDetails: React.FC<TodoDetailsProps> = ({ isOpen, todoId, onClose }) => {
  const { client } = useBackendClient();
  const [isEditMode, setIsEditMode] = useState(false);
  const { confirmAction, isEditFormDirty } = useTodoContext();
  const [_, setSelectedChatId] = useQueryState<number>('chatId', parseAsInteger);

  const fetchTodoDetailsWithGetToken = async (url: string) => {
    const response = await client.get<unknown>(url);
    return response.data as ITodoDto | null;
  };

  const updateTodoWithGetToken = async (url: string, options: { arg: IUpdateTodoDto }) => {
    return updateTodo(url, { arg: { client, updateTodo: options.arg } });
  };

  const { data: todo, isLoading } = useSWR(
    todoId ? getApiUrl(`${API_ENDPOINTS.TODOS}/${todoId}`) : null,
    fetchTodoDetailsWithGetToken
  );
  const { trigger: markTodoAsDone } = useSWRMutation(
    todoId ? getApiUrl(`${API_ENDPOINTS.TODOS}/${todoId}`) : null,
    updateTodoWithGetToken
  );
  const { data: entityLinks } = useGetEntityLink({
    entityId: todo?.id ?? -1,
    entityType: 'todos',
  });

  const { trigger: deleteTodo } = useDeleteTodo();
  const handleEditClick = () => {
    setIsEditMode(true);
  };

  const handleMarkAsDone = async () => {
    try {
      await confirmAction('markAsDone', null);
      // do nothing, the user cancelled the action
    } catch (_err) {
      if (!todo) return;

      await markTodoAsDone({
        ...todo,
        doneDate: toNaiveISOString(new Date()),
      });
      mutate(getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED));
      onClose?.();
    }
  };

  const handleDeleteClick = async () => {
    try {
      await confirmAction('removeTodo', null);
      // do nothing, the user cancelled the action
    } catch (_err) {
      if (!todo) return;

      await deleteTodo({
        todoId: todo.id,
      });
      mutate(getApiUrl(API_ENDPOINTS.TODOS_ACCEPTED));
      onClose?.();

      toast.info({ title: 'To-do deleted' });
    }
  };

  const handleSaveClick = () => {
    setIsEditMode(false);
  };

  const handleCloseModal = () => {
    if (isEditMode) {
      if (!isEditFormDirty) {
        return setIsEditMode(false);
      }

      confirmAction('editTodo', null)
        .then(() => {
          // User cancelled the action, do nothing
        })
        .catch(() => {
          setIsEditMode(false);
        });
    } else {
      onClose?.();
    }
  };

  const appendMessages = useChats((state) => state.appendMessages);

  const chatMessages = useMemo(() => {
    const isChatLinkedEntity = (entity: any): entity is ChatLinkedEntityInfo =>
      entity.entityType === 'chats';

    return entityLinks?.filter(isChatLinkedEntity)?.[0];
  }, [entityLinks]);

  const appliances = useMemo(() => {
    const isApplianceLinkedEntity = (entity: any): entity is ApplianceLinkedEntityInfo =>
      entity.entityType === 'appliances';

    return entityLinks?.filter(isApplianceLinkedEntity);
  }, [entityLinks]);

  const contextualPrompts = useMemo(() => {
    const isContextualPromptLinkedEntity = (
      entity: any
    ): entity is ContextualPromptLinkedEntityInfo => entity.entityType === 'contextual_prompts';

    return entityLinks?.filter(isContextualPromptLinkedEntity);
  }, [entityLinks]);

  useEffect(() => {
    const chatEntityLinkedToTodo = entityLinks?.find((link) => link.entityType === 'chats');

    if (chatEntityLinkedToTodo?.id) {
      setSelectedChatId(chatEntityLinkedToTodo.id);
    }
  }, [setSelectedChatId, entityLinks]);

  useEffect(() => {
    if (chatMessages?.id && chatMessages?.lastMessages) {
      appendMessages(chatMessages.lastMessages, chatMessages.id);
    }
  }, [appendMessages, chatMessages]);

  return (
    <Dialog open={isOpen} onOpenChange={handleCloseModal}>
      <DialogContent className="flex flex-col pt-[60px]" showCloseButton={false}>
        <div className="absolute top-4 right-4 flex gap-4 items-center bg-white">
          {!isLoading && !isEditMode && todo?.type !== 'systemCreated' && (
            <>
              <Button
                size="xs"
                variant="ghost"
                onClick={handleMarkAsDone}
                disabled={!!todo?.doneDate}
              >
                <HugeiconsIcon icon={Tick02Icon as unknown as IconSvgObject} size={12} /> Mark as
                Done
              </Button>
              <Button size="icon" variant="link" onClick={handleEditClick}>
                <HugeiconsIcon icon={PencilEdit01Icon as unknown as IconSvgObject} size={24} />
              </Button>
              <Button size="icon" variant="link" onClick={handleDeleteClick}>
                <HugeiconsIcon
                  icon={Delete02Icon as unknown as IconSvgObject}
                  size={24}
                  color="var(--color-red-500)"
                />
              </Button>
            </>
          )}
          <Button size="icon" variant="link" onClick={() => handleCloseModal()}>
            <HugeiconsIcon
              icon={SolidStandard.MultiplicationSignIcon as unknown as IconSvgObject}
              size={24}
            />
          </Button>
        </div>
        {isLoading || !todo ? (
          <div className="w-full h-full flex items-center justify-center">
            <Spinner color="var(--colors-yellow-500)" size={50} />
          </div>
        ) : (
          <div className="flex-1 flex flex-col min-h-0">
            {isEditMode ? (
              <EditTodoDetails todo={todo} onClick={handleSaveClick} />
            ) : (
              <TodoDetailsContent
                todo={todo}
                chatMessages={chatMessages}
                contextualPrompts={contextualPrompts}
                appliances={appliances}
                onClose={onClose}
              />
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
