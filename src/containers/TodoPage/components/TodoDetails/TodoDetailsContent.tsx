'use client';

import { ITodoDto } from '@/api/todos/types';
import { Button } from '@/components/ui/button';
import { Button as ActionButton } from '@/components/Button';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { Calendar04Icon, LinkSquare01Icon } from '@hugeicons-pro/core-stroke-standard';
import Image from 'next/image';
import { Spinner } from '@/components/Spinner';
import { format } from 'date-fns';
import { Composer } from '@/components/Composer';
import { google } from 'calendar-link';
import { ApprovalActions } from '../Todo/actions/ApprovalActions';
import {
  ApplianceLinkedEntityInfo,
  ChatLinkedEntityInfo,
  ContextualPromptLinkedEntityInfo,
} from '@/api/entityLink';
import { Message } from '@/components/Message';
import useMessageSender from '@/hooks/useMessageSender';
import { ButtonSize, ButtonType } from '@/components/Button/Button.types';
import { useDeleteLink } from '@/hooks/useEntityLinkService';
import { useRouter } from 'next/navigation';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { toast } from '@/components/ui/sonner';
import { useSWRConfig } from 'swr';

import clsx from 'clsx';

interface ITodoDetailsContentProps {
  todo?: ITodoDto | null;
  chatMessages?: ChatLinkedEntityInfo;
  contextualPrompts?: ContextualPromptLinkedEntityInfo[] | null;
  appliances?: ApplianceLinkedEntityInfo[] | null;
  onClose?: () => void;
}

export const TodoDetailsContent = ({
  todo,
  appliances,
  chatMessages,
  contextualPrompts = [],
  onClose,
}: ITodoDetailsContentProps) => {
  const router = useRouter();
  const { sendMessage } = useMessageSender();
  const { trigger: deleteLink } = useDeleteLink();
  const { mutate } = useSWRConfig();

  if (!todo) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Spinner color="var(--colors-yellow-500)" size={100} />
      </div>
    );
  }

  const description = `
  ${todo.description ?? ''}
  
  📲 View in the Hey Alfie app: 
  ${window && window.location.href}
  `;

  const googleCalendarLink = google({
    title: todo.name,
    description,
    start: todo.dueDate ?? new Date(),
    allDay: true,
  });

  const handleDeleteLinkClick = (appliance: ApplianceLinkedEntityInfo, todo: ITodoDto) =>
    deleteLink(
      {
        entityTypeA: 'appliances',
        entityIdA: `${appliance.id}`,
        entityTypeB: 'todos',
        entityIdB: `${todo.id}`,
      },
      {
        onSuccess: () => {
          toast.success({ title: 'Appliance link removed successfully' });
          mutate(getApiUrl(`${API_ENDPOINTS.ENTITY_LINKS}`));
        },
        onError: () => {
          toast.info({ title: 'Failed to remove appliance link' });
        },
      }
    );

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex-1 overflow-y-auto scrollbar">
        <div className="flex flex-col gap-3 mb-4 justify-start pt-5 items-start">
          <p className="text-lg font-[Quasimoda] leading-[27px]">{todo.name}</p>
          {todo.type === 'systemCreated' && (
            <p className="text-xs font-[Quasimoda] leading-[21px] text-yellow-600 bg-yellow-50 border border-yellow-500 px-[5px] py-0.5 rounded-full">
              Suggested by Alfie
            </p>
          )}
          <p className="text-sm font-[Quasimoda] leading-[21px] text-gray-600 flex items-center gap-1">
            <HugeiconsIcon icon={Calendar04Icon as unknown as IconSvgObject} size={17} />
            Due by {todo.dueDate ? format(todo.dueDate, 'dd/MM/yyyy') : 'not provided'}
          </p>
          {todo.type !== 'systemCreated' && todo.dueDate && (
            <Button
              size="xs"
              variant="ghost"
              className="shrink-0 text-gray-600"
              onClick={() => window.open(googleCalendarLink, '_blank')}
            >
              <Image src="google-calendar.svg" alt="Google Calendar Icon" width={20} height={20} />
              Add to Google Calendar
            </Button>
          )}
        </div>

        <div className="mb-4">
          <p
            className={clsx('text-gray-600 text-sm leading-[21px] font-[Quasimoda]', {
              'mb-2': !todo.description,
            })}
          >
            Description
          </p>
          <div style={{ minHeight: todo.description ? '60px' : '30px' }}>
            {todo.description && (
              <p className="text-sm font-[Quasimoda] leading-[21px] text-gray-800 mb-2">
                {todo.description}
              </p>
            )}
            {/* <div className="flex flex-col gap-1 items-start">
          {appliances?.map((appliance) => (
            <Link
              key={appliance.id}
              text={`${appliance.entity.brand} ${appliance.entity.model}`}
              onClick={() => router.push(API_ENDPOINTS.PROPERTY_PROFILE_APPLIANCES)}
              removeAction={() => handleDeleteLinkClick(appliance, todo)}
            />
          ))}
        </div> */}
            {todo.type === 'systemCreated' && (
              <div className="mt-4">
                <ApprovalActions todo={todo} onReject={onClose} />
              </div>
            )}
          </div>
        </div>

        {chatMessages && (
          <div className="mt-6 pb-6">
            <p className="font-[Quasimoda] text-xl text-gray-400 font-bold mb-2.5 flex justify-between items-center">
              Last message from Alfie
              <HugeiconsIcon
                icon={LinkSquare01Icon as unknown as IconSvgObject}
                size={24}
                color="var(--colors-blue-1000)"
                onClick={() => {
                  if (chatMessages?.id) {
                    window.open(`/chats/${chatMessages.id}`, '_blank');
                  }
                }}
              />
            </p>
            <div>
              <Message
                message={chatMessages?.lastMessages?.[0]}
                messages={chatMessages?.lastMessages ?? []}
              />
            </div>
          </div>
        )}
      </div>

      <div className="flex-shrink-0 bg-white">
        {!chatMessages && (
          <p className="font-[Quasimoda] text-xl leading-[30px] font-bold mb-3">
            What can I help you with?
          </p>
        )}
        <Composer
          onSend={(value, attachments) => {
            sendMessage(value, attachments, { entityType: 'todos', id: todo.id });
          }}
        />
        {todo.type !== 'systemCreated' && (
          <div className={'flex flex-col md:flex-row gap-4 mt-4 items-start'}>
            {contextualPrompts && contextualPrompts?.length > 0 ? (
              contextualPrompts.map((contextualPrompt) => (
                <ActionButton
                  multiLine
                  key={contextualPrompt.id}
                  type={ButtonType.SECONDARY}
                  size={ButtonSize.BASE}
                  onClick={() =>
                    sendMessage(contextualPrompt.prompt, [], { entityType: 'todos', id: todo.id })
                  }
                >
                  {contextualPrompt.prompt}
                </ActionButton>
              ))
            ) : (
              <p className="flex items-center gap-2 text-base">
                <Spinner /> Generating prompts
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
