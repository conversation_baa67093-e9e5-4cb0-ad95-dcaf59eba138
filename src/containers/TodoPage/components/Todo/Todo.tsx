import { Typography } from '@/components/Typography/Typography';
import { format } from 'date-fns';
import { FC } from 'react';
import styles from './Todo.module.scss';
import { TodoCheckbox } from './TodoCheckbox';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { ArrowRight01Icon } from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';
import { cn } from '@/lib/utils';
import { ITodoDto } from '@/api/todos/types';
import { ITodoActionsProps } from './types';
import clsx from 'clsx';

interface ITodoProps {
  todo: ITodoDto;
  onChecked?: () => void;
  onClick?: () => void;
  ActionsComponent?: FC<ITodoActionsProps>;
}

export const Todo: FC<ITodoProps> = ({ todo, ActionsComponent, onChecked, onClick = () => {} }) => (
  <div
    role="button"
    className="flex justify-between items-center pt-4 pb-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer relative overflow-hidden sm:px-4"
    onClick={onClick}
  >
    <div className="flex gap-2 items-start w-full pr-28">
      {onChecked && (
        <div role="button" className="mb-1" onClick={(e) => e.stopPropagation()}>
          <TodoCheckbox checked={!!todo.doneDate} onChange={onChecked} />
        </div>
      )}
      <div className="flex flex-col flex-1 gap-2 min-w-0">
        <p
          className={cn(
            'ellipsis text-base leading-[18px] text-gray-900 font-[Quasimoda]',
            !!todo.doneDate && 'line-through'
          )}
        >
          {todo.name}
        </p>
        {todo.description && (
          <p
            className={cn(
              'ellipsis text-xs text-gray-600 font-[Quasimoda]',
              !!todo.doneDate && 'line-through'
            )}
          >
            {todo.description}
          </p>
        )}
        {ActionsComponent && <ActionsComponent todo={todo} />}
      </div>
    </div>
    <div className={clsx(styles.dueDateContainer, 'absolute right-4 top-1/2 -translate-y-1/2')}>
      {todo.dueDate && (
        <Typography variant="body-s" font="quasimoda" className={styles.dueDate}>
          Due: {format(todo.dueDate, 'dd/MM/yy')}
        </Typography>
      )}
      <HugeiconsIcon
        icon={ArrowRight01Icon as unknown as IconSvgObject}
        size={20}
        className={styles.arrowIcon}
        color="var(--color-gray-600)"
      />
    </div>
  </div>
);
