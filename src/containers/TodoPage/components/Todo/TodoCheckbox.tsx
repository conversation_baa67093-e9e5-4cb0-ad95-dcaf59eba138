import React from 'react';
import styles from './TodoCheckbox.module.scss';
import { Tick02Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';

type Props = {
  checked: boolean;
  onChange: () => void;
};

export const TodoCheckbox: React.FC<Props> = ({ checked, onChange }) => {
  return (
    <button
      className={`${styles.checkbox} ${checked ? styles.checked : ''}`}
      onClick={() => onChange()}
      aria-pressed={checked}
    >
      {checked && (
        <HugeiconsIcon
          icon={Tick02Icon as unknown as IconSvgObject}
          size={13}
          className={styles.arrowIcon}
        />
      )}
    </button>
  );
};
