import { MultiplicationSignIcon, ArrowRight01Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';

interface ILinkProps {
  text: string;
  onClick: () => void;
  removeAction: () => void;
}

export const Link = ({ text, onClick, removeAction }: ILinkProps) => (
  <div className="bg-green-50 border border-green-600 pl-2 pr-1 py-1 flex gap-4 items-center rounded-sm">
    <button className="text-green-900 text-xs flex items-center" onClick={onClick}>
      {text} <HugeiconsIcon icon={ArrowRight01Icon as unknown as IconSvgObject} size={12} />
    </button>
    <HugeiconsIcon
      icon={MultiplicationSignIcon as unknown as IconSvgObject}
      size={12}
      onClick={removeAction}
    />
  </div>
);
