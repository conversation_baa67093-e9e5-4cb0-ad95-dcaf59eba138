import { motion, AnimatePresence } from 'framer-motion';
import { Todo } from '../Todo';
import { ITodoDto } from '@/api/todos/types';
import { ITodoActionsProps } from '../Todo/types';
import { FC } from 'react';

interface ITodosListProps {
  todos: ITodoDto[];
  onChecked?: (id: ITodoDto['id']) => void;
  onClick: (id: ITodoDto['id']) => void;
  ActionsComponent?: FC<ITodoActionsProps>;
}

export const TodoList = ({ todos, ActionsComponent, onClick, onChecked }: ITodosListProps) => (
  <div className="flex flex-col">
    <AnimatePresence>
      {todos.map((todo) => (
        <motion.div
          key={todo.id}
          initial={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0, margin: 0, padding: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Todo
            todo={{ ...todo }}
            onChecked={onChecked ? () => onChecked(todo.id) : undefined}
            onClick={() => onClick(todo.id)}
            ActionsComponent={ActionsComponent}
          />
        </motion.div>
      ))}
    </AnimatePresence>
  </div>
);
