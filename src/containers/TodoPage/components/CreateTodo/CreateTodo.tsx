'use client';

import * as SolidStandard from '@hugeicons-pro/core-solid-standard';
import { Dialog, DialogTrigger, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useRef, useState } from 'react';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { CreateTodoForm } from './CreateTodoForm';
import { useTodoContext } from '../../useTodoContext';
import { parseAsBoolean, useQueryState } from 'nuqs';

interface CreateTodoProps {
  hideTriggerButton?: boolean;
}

export const CreateTodo: React.FC<CreateTodoProps> = ({ hideTriggerButton }) => {
  const [open, setOpen] = useQueryState<boolean>('createTodo', parseAsBoolean.withDefault(false));
  const [isFormDirty, setIsFormDirty] = useState(false);
  const triggerButtonRef = useRef<HTMLButtonElement>(null);
  const { confirmAction } = useTodoContext();

  const handleOnSubmit = () => {
    setTimeout(() => {
      setOpen(false);
      setIsFormDirty(false);
      triggerButtonRef.current?.focus();
    }, 0);
  };

  const handleCloseModal = async () => {
    if (isFormDirty) {
      try {
        await confirmAction('closeWithUnsaved', null);
        setOpen(true);
      } catch (_err) {
        setOpen(false);
        setIsFormDirty(false);
      }
    } else {
      setOpen(false);
    }
  };

  const handleOpenChange = async (newOpen: boolean) => {
    if (newOpen) {
      setOpen(true);
    } else {
      await handleCloseModal();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {!hideTriggerButton && (
          <Button ref={triggerButtonRef} size="xs">
            <HugeiconsIcon icon={SolidStandard.Add01Icon as unknown as IconSvgObject} /> Add a new
            to-do
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="pt-[60px]" showCloseButton={false}>
        <div className="absolute top-4 right-4 flex gap-4 items-center">
          <Button size="icon" variant="link" onClick={handleCloseModal}>
            <HugeiconsIcon
              icon={SolidStandard.MultiplicationSignIcon as unknown as IconSvgObject}
              size={24}
            />
          </Button>
        </div>
        <CreateTodoForm onSubmit={handleOnSubmit} onDirtyChange={setIsFormDirty} />
      </DialogContent>
    </Dialog>
  );
};
