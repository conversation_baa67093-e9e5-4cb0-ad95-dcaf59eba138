'use client';

import React, { createContext, useState, useCallback } from 'react';

type DialogType = 'markAsDone' | 'removeTodo' | 'closeWithUnsaved' | 'editTodo' | null;

interface DialogState {
  type: DialogType;
  todoId: number | null;
  resolve?: () => void;
  reject?: () => void;
}

interface TodoDialogContextProps {
  dialog: DialogState;
  isEditFormDirty: boolean;
  setIsEditFormDirty: (value: boolean) => void;
  confirmAction: (type: DialogType, todoId: number | null) => Promise<void>;
  cancelDialog: () => void;
  completeDialog: () => void;
}

export const TodoContext = createContext<TodoDialogContextProps | undefined>(undefined);

export const TodoProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [dialog, setDialog] = useState<DialogState>({ type: null, todoId: null });
  const [isEditFormDirty, setIsEditFormDirty] = useState(false);

  const confirmAction = useCallback((type: DialogType, todoId: number | null) => {
    return new Promise<void>((resolve, reject) => {
      setDialog({ type, todoId, resolve, reject });
    });
  }, []);

  const cancelDialog = useCallback(() => {
    dialog.reject?.();
    setDialog({ type: null, todoId: null });
  }, [dialog]);

  const completeDialog = useCallback(() => {
    dialog.resolve?.();
    setDialog({ type: null, todoId: null });
  }, [dialog]);

  const contextValue = React.useMemo(
    () => ({
      dialog,
      isEditFormDirty,
      setIsEditFormDirty,
      confirmAction,
      cancelDialog,
      completeDialog,
    }),
    [dialog, isEditFormDirty, confirmAction, cancelDialog, completeDialog]
  );

  return <TodoContext.Provider value={contextValue}>{children}</TodoContext.Provider>;
};
