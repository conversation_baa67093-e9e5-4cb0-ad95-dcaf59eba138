export function getUploadErrorMessage(error: unknown, fileName: string): string {
  const defaultError = `Failed to upload '${fileName}'. Please try again later.`;
  const tooLargeMessage = `'${fileName}' is too large. The maximum file size allowed is 20MB.`;
  const unsupportedFormatMessage = `'${fileName}' has an unsupported file format. Please use PDF, PNG, JPG, JPEG, WEBP, GIF, HEIC, or HEIF files.`;

  if (typeof error !== 'object' || error === null) {
    return defaultError;
  }

  const err = error as Record<string, unknown>;
  const message = typeof err.message === 'string' ? err.message : '';
  const status = err.status;

  if (message.includes('Network Error')) {
    return `Network error while uploading '${fileName}'. Please check your connection and try again.`;
  }

  if (message.includes('too large')) {
    return tooLargeMessage;
  }

  let detail: unknown = undefined;
  if (
    err.response &&
    typeof err.response === 'object' &&
    'data' in err.response &&
    err.response.data &&
    typeof err.response.data === 'object' &&
    'detail' in err.response.data
  ) {
    detail = (err.response as { data: { detail?: unknown } }).data.detail;
  }

  if (typeof detail === 'string') {
    const detailLower = detail.toLowerCase();

    if (detailLower.includes('size limit exceeded')) {
      return tooLargeMessage;
    }

    if (detailLower.includes('unsupported')) {
      return unsupportedFormatMessage;
    }

    return `Failed to upload '${fileName}': ${detail}`;
  }

  switch (status) {
    case 400:
      return `'${fileName}' could not be uploaded due to a validation error. Please check the file and try again.`;
    case 413:
      return tooLargeMessage;
    case 500:
      return `Server error while uploading '${fileName}'. Please try again later.`;
  }

  return defaultError;
}
