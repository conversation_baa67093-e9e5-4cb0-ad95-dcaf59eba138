import { DocumentPreview } from '@/components/DocumentPreview';
import { Typography } from '@/components/Typography/Typography';
import { CategorizedFile } from '@/types/file';
import React from 'react';
import { mapFetchedFilesToDocuments } from '@/utils/messageUtils';

interface ICategoryWithItemsProps {
  category: string;
  files: CategorizedFile[];
}

export const CategoryWithFiles = ({ category, files }: ICategoryWithItemsProps) => {
  const dtos = mapFetchedFilesToDocuments(files);
  return (
    <div key={category} className="mb-6">
      <Typography variant="body-s" font="quasimoda" className="mb-3 font-bold">
        {category}
      </Typography>
      <div className="flex flex-col gap-4">
        {dtos.map((dto) => (
          <DocumentPreview key={dto.id} dto={dto} />
        ))}
      </div>
    </div>
  );
};
