'use client';

import { filter, groupBy, lowerCase, upperFirst } from 'lodash';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button } from '@/components/Button';
import { ButtonType } from '@/components/Button/Button.types';
import { CategoryWithFiles } from './components/CategoryWithFiles';
import { EmptyState } from './components/EmptyState/EmptyState';
import { Spinner } from '@/components/Spinner';
import { useFiles } from './useFiles';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { isCategorizedFile } from '@/types/file';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';
import { Breadcrumbs } from '@/components/Breadcrumbs';

interface IFilesProps {}

export const FilesPage: React.FC<IFilesProps> = () => {
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { uploadFiles, uploadingFiles, deleteNotification, files, notifications } = useFiles({
    setUploadContext: 'filesPage',
    getUploadContext: null,
    onLoaded: useCallback(() => setIsInitialLoading(false), []),
  });

  const firstErrorNotification = notifications.find((n) => n.severity === 'error');
  const firstInfoNotification = notifications.find((n) => n.severity === 'info');
  const firstWarningNotification = notifications.find((n) => n.severity === 'warning');

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSelectFileClick = () => {
    fileInputRef.current?.click();
  };

  const filesSortedByCategory = groupBy(
    filter(files, (file) => isCategorizedFile(file)),
    (file) => (file.category ? upperFirst(lowerCase(file.category)) : 'Other')
  );

  const renderedCategoriesWithFiles = useMemo(
    () =>
      Object.entries(filesSortedByCategory).map(([category, files]) => (
        <CategoryWithFiles key={category} category={category} files={files} />
      )),
    [filesSortedByCategory]
  );

  const renderedProcessingFiles = useMemo(
    () =>
      uploadingFiles.length > 0 ? (
        <div className="mb-6">
          <h2 className="text-[16px] font-bold mb-[8px]">Your document</h2>
          {uploadingFiles.length > 0 && (
            <>
              <div className="pt-2 pb-0 overflow-x-auto w-full [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
              </div>
            </>
          )}
        </div>
      ) : null,
    [uploadingFiles]
  );

  if (isInitialLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Spinner color="var(--colors-yellow-500)" size={100} />
      </div>
    );
  }

  return (
    <div className="w-full px-4 pb-12 md:pb-6 mx-auto box-border h-screen max-w-[798px] overflow-y-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
      <Breadcrumbs
        path={[{ value: 'Property Profile', url: '/property-profile' }, { value: 'Files' }]}
      />
      <h1 className="text-[24px] font-bold mb-[14px]">Files</h1>
      <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
      {files.length === 0 ? (
        <EmptyState onClick={handleSelectFileClick} />
      ) : (
        <div className="max-w-[798px] w-full mx-auto overflow-visible">
          <h2 className="text-[16px] font-bold mb-[8px]">Upload documents</h2>
          <p className="text-[16px] mb-4">
            All documents uploaded to Hey Alfie will show in your Files here. These documents
            contain information that helps Alfie provide personalised property management for you.
          </p>
          <div className="flex gap-2 mt-2 mb-4">
            <Button type={ButtonType.PRIMARY} onClick={handleSelectFileClick}>
              Upload documents
            </Button>
          </div>
          <div className="flex flex-col gap-3 mt-4 mb-4">
            {firstErrorNotification && (
              <NotificationAlert
                notification={firstErrorNotification}
                onClose={deleteNotification}
              />
            )}
            {firstWarningNotification && (
              <NotificationAlert
                notification={firstWarningNotification}
                onClose={deleteNotification}
              />
            )}
            {firstInfoNotification && (
              <NotificationAlert
                notification={firstInfoNotification}
                onClose={deleteNotification}
              />
            )}
          </div>
          {renderedProcessingFiles}
          {renderedCategoriesWithFiles}
        </div>
      )}
    </div>
  );
};
