import { fetchDocuments, uploadDocument } from '@/api/documents';
import { useCallback, useEffect, useMemo } from 'react';
import { getFileType } from '@/utils/fileUtils';
import { LocalFile, UniversalFile, UploadedFile } from '@/types/file';
import { useNotifications } from '@/hooks/useNotifications';
import { Context } from '@/api/notifications';
import useBackendClient from '@/hooks/useBackendClient';
import { mapDocumentsToFetchedFile } from '@/utils/messageUtils';
import { useChats } from '@/hooks/useChats';
import { create } from 'zustand';

interface UseAppliancesDocumentUploadOptions {
  setUploadContext: Context;
  getUploadContext: Context | null;
  pollingInterval?: number;
  onLoaded?: () => void;
  onChanged?: () => void;
}

interface ErrorWithResponse {
  response?: {
    data?: {
      detail?: string;
    };
  };
}

const SUPPORTED_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/heic',
  'image/heif',
];

const SUPPORTED_EXTENSIONS = ['.pdf', '.png', '.jpg', '.jpeg', '.webp', '.gif', '.heic', '.heif'];

type Store = {
  pollingIntervalRef: ReturnType<typeof setInterval> | null;
  isUploading: boolean;
  files: UniversalFile[];
  upsertFile: (file: UniversalFile) => void;
  removeFile: (id: string) => void;
  setIsUploading: (val: boolean) => void;
  setPollingIntervalRef: (ref: ReturnType<typeof setInterval> | null) => void;
};

const useStore = create<Store>((set) => ({
  pollingIntervalRef: null,
  isUploading: false,
  files: [],
  upsertFile: (file: UniversalFile) =>
    set((state) => {
      const differentId = (f: UniversalFile) =>
        'documentId' in f && 'documentId' in file
          ? f.documentId !== file.documentId
          : f.id !== file.id;
      const current = [...state.files.filter(differentId), file];
      current.sort((a, b) => a.id.localeCompare(b.id));
      return { files: current };
    }),
  removeFile: (id: string) =>
    set((state) => ({
      files: state.files.filter((f) => f.id !== id),
    })),
  setIsUploading: (val: boolean) => set({ isUploading: val }),
  setPollingIntervalRef: (ref: NodeJS.Timeout | null) => set({ pollingIntervalRef: ref }),
}));

export const useFiles = ({
  pollingInterval = 5000,
  onLoaded,
  getUploadContext,
  setUploadContext,
  onChanged,
}: UseAppliancesDocumentUploadOptions) => {
  const { client } = useBackendClient();
  const { files, upsertFile, removeFile, isUploading, setIsUploading, setPollingIntervalRef } =
    useStore();
  const { updateDocumentsInChats } = useChats();
  const isAnyFileBeingProcessed = useMemo(
    () => files.some((file) => file.status === 'uploading'),
    [files]
  );

  const {
    notifications,
    deleteNotifications,
    deleteNotification,
    fetchNotifications,
    pushNotification,
  } = useNotifications({
    context: getUploadContext,
  });

  const filesHash = useMemo(() => {
    const sorted = [...files].sort((a, b) => a.id.localeCompare(b.id));
    return JSON.stringify(sorted);
  }, [files]);

  useEffect(() => {
    onChanged?.();
  }, [filesHash, onChanged]);

  const validateFiles = useCallback((files: File[]) => {
    const supported: File[] = [];
    const unsupported: string[] = [];

    files.forEach((file) => {
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      const isSupported =
        SUPPORTED_TYPES.includes(file.type) || SUPPORTED_EXTENSIONS.includes(fileExtension);

      if (!isSupported) {
        unsupported.push(file.name);
      } else {
        supported.push(file);
      }
    });

    return { supported, unsupported };
  }, []);

  const shouldPoll = !files || files.length === 0 || isAnyFileBeingProcessed;
  const pollDocuments = useCallback(async () => {
    if (!shouldPoll) {
      return;
    }
    const dtos = await fetchDocuments(client, getUploadContext);
    mapDocumentsToFetchedFile(dtos).forEach(upsertFile);
    updateDocumentsInChats(dtos);
    onLoaded?.();
    await fetchNotifications();
  }, [
    client,
    fetchNotifications,
    getUploadContext,
    onLoaded,
    shouldPoll,
    updateDocumentsInChats,
    upsertFile,
  ]);

  useEffect(() => {
    const interval = setInterval(pollDocuments, pollingInterval);
    setPollingIntervalRef(interval);
    void pollDocuments();

    return () => {
      if (interval) {
        clearInterval(interval);
      }
      setPollingIntervalRef(null);
    };
  }, [pollDocuments, pollingInterval, setPollingIntervalRef]);

  const uploadFiles = useCallback(
    async (files: File[]) => {
      const { supported, unsupported } = validateFiles(files);

      if (unsupported.length > 0) {
        pushNotification({
          id: `unsupported-files-${new Date()}`,
          systemId: null,
          type: 'document_error',
          fileName: '',
          severity: 'error',
          title: `Unsupported file format`,
          message: `${unsupported.map((name) => `'${name}'`).join(', ')} ${unsupported.length === 1 ? 'has' : 'have'} not been processed. Alfie is able to read documents and photos including PDF, PNG, JPG, JPEG, WEBP, GIF, HEIC, and HEIF file formats.`,
        });
      }

      if (supported.length === 0) return;

      setIsUploading(true);

      const uploadPromises = supported.map(async (file) => {
        const tempId = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const uploadingFile: LocalFile = {
          id: tempId,
          name: file.name,
          type: getFileType(file.name, file.type),
          blob: file,
          createdAt: new Date().toISOString(),
          status: 'uploading',
          sizeInKiloBytes: file.size,
          browserMimeType: file.type,
        };

        upsertFile(uploadingFile);

        try {
          await deleteNotifications();
          const document = await uploadDocument(file, client, setUploadContext);
          const uploadedFile: UploadedFile = {
            ...uploadingFile,
            documentId: document.id,
          };

          upsertFile(uploadedFile);
          return document;
        } catch (error) {
          console.error('Upload failed for file:', file.name, error);
          const detail = (error as ErrorWithResponse)?.response?.data?.detail;
          pushNotification({
            id: `notification-for-file-${file.name}`,
            systemId: null,
            type: 'document_error',
            severity: 'error',
            fileName: file.name,
            title: `Failed to upload '${file.name}'`,
            message: detail ? detail : `An unexpected issue occurred. Please try again.`,
          });
          removeFile(tempId);
        }
      });

      setIsUploading(false);
      return await Promise.all(uploadPromises);
    },
    [
      client,
      deleteNotifications,
      pushNotification,
      removeFile,
      setIsUploading,
      setUploadContext,
      upsertFile,
      validateFiles,
    ]
  );

  const uploadingFiles = useMemo(
    (): UniversalFile[] => files.filter((file) => file.status === 'uploading'),
    [files]
  );

  return {
    uploadFiles,
    uploadingFiles,
    isUploading,
    files,
    deleteNotification,
    notifications,
  };
};
