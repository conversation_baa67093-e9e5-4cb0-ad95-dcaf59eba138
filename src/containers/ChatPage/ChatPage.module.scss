.container {
  display: flex;
  width: 100%;
  height: 100dvh;
  max-height: 100dvh;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.chatContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  max-width: 960px;
  margin: 0 auto;
  width: 100%;
  padding: 0 16px;
  box-sizing: border-box;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    padding: 0 var(--spacing-2);
    max-width: 100dvw;
  }
}

.onboardingContainer {
  display: flex;
  flex-direction: column;
}

.composerContainer {
  padding: 0 0 24px 0;
  max-width: 960px;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 1;

  &.locked {
    margin-top: 36px;
  }

  @media (max-width: 768px) {
    padding: 0 0 var(--spacing-2) 0;
  }

  :global(.composer) {
    margin-left: 0;
  }
}

.sidebarWrapper {
  width: 260px;
  height: 100%;
  transition: transform 0.3s ease-in-out;
  flex-shrink: 0;
  z-index: 40;

  @media (max-width: 700px) {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    transform: translateX(-100%);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }
}

.sidebarOpen {
  .sidebarWrapper {
    transform: translateX(0);
  }

  @media (max-width: 700px) {
    .main {
      filter: blur(3px);
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 30;
}
