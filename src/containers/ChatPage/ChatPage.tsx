'use client';

import React, { useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { Composer } from '@/components/Composer';
import { Messages } from '@/components/Messages';
import { useMessages } from '@/hooks/useMessages';
import styles from './ChatPage.module.scss';
import { ChatPageProps } from './ChatPage.types';
import { useChats } from '@/hooks/useChats';
import classNames from 'classnames';
import useChatParams from '@/hooks/useChatParams';
import useMessageSender from '@/hooks/useMessageSender';
import { useGetEntityLink } from '@/hooks/useEntityLinkService';
import { CheckListIcon } from '@hugeicons-pro/core-solid-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { TodoLinkedEntityInfo } from '@/api/entityLink';
import clsx from 'clsx';
import useBackendClient from '@/hooks/useBackendClient';

export const ChatPage: React.FC<ChatPageProps> = ({}) => {
  const {
    hasMore,
    fetchMessages,
    loadMoreMessages,
    resetStore: resetMessagesStore,
  } = useMessages();

  const { client } = useBackendClient();
  const { chatId, query } = useChatParams();
  const { chats } = useChats();
  const router = useRouter();
  const messages = useMemo(
    () => chats.find((chat) => chat.id === chatId)?.messages || [],
    [chats, chatId]
  );
  const isChatLocked = !!messages[0]?.additionalData?.jobSummary?.jobId;
  const { sendMessage } = useMessageSender();

  useEffect(() => {
    if (chatId && messages.length === 0) {
      fetchMessages(chatId, client)
        .then(({ isValid, hasMessages }) => {
          if (!isValid || !hasMessages) {
            console.error('Chat ID is not valid or has no messages');
            router.replace('/');
            return;
          }
          console.log('Chat ID validated successfully - navigating to chat URL');
        })
        .catch((error) => {
          console.error('Failed to validate and fetch messages:', error);
          router.replace('/');
        });
    }
  }, [chatId, fetchMessages, messages.length, chats, router, client]);

  // Cleanup chat-specific UI state when leaving a chat
  useEffect((): (() => void) => {
    return () => {
      resetMessagesStore();
    };
  }, [chatId, resetMessagesStore]);

  useEffect(() => {
    if (!chatId && query) {
      sendMessage(query).then(() => console.log('message sent'));
    }
  }, [chatId, query, sendMessage]);

  const { data: entityLinks } = useGetEntityLink({
    entityId: chatId ?? -1,
    entityType: 'chats',
  });

  const firstTodoEntityType = useMemo(() => {
    const isTodoEntity = (link: any): link is TodoLinkedEntityInfo => {
      return link.entityType === 'todos';
    };

    return entityLinks?.filter(isTodoEntity)[0];
  }, [entityLinks]);

  return (
    <>
      {firstTodoEntityType?.entity.name && (
        <div className="absolute top-[64px] left-0 w-full p-2 bg-neutral-10 z-40 border border-neutral-100">
          <a
            href={`/todo-list?todo=${firstTodoEntityType.entity.id}`}
            className="flex items-center gap-1 text-sm text-neutral-800 min-w-0"
          >
            <HugeiconsIcon
              icon={CheckListIcon as unknown as IconSvgObject}
              size={16}
              className="shrink-0"
            />
            <span className="flex-1 min-w-0 truncate">
              Linked to to-do: {firstTodoEntityType.entity.name}
            </span>
          </a>
        </div>
      )}
      <div
        className={clsx(styles.chatContainer, { '!pt-[38px]': !!firstTodoEntityType?.entity.name })}
      >
        <Messages
          chatId={chatId}
          hasMore={hasMore}
          onLoadMore={async () => {
            if (chatId) {
              await loadMoreMessages(chatId, client);
              console.log('more messages loaded');
            }
          }}
        />
        <div
          className={classNames(styles.composerContainer, {
            [styles.locked]: isChatLocked,
          })}
        >
          <Composer isLocked={isChatLocked} />
        </div>
      </div>
    </>
  );
};
