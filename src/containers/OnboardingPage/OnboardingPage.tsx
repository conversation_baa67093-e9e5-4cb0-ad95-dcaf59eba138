'use client';

import React from 'react';
import { Onboarding } from '@/components/Onboarding/Onboarding';
import styles from './OnboardingPage.module.scss';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useUser } from '@clerk/nextjs';

export const OnboardingPage: React.FC = () => {
  const { user, isLoaded } = useUser();
  const { reset } = useOnboarding();

  const completeOnboarding = async (addressSendRequest: Promise<unknown>) => {
    try {
      await Promise.all([addressSendRequest]);
      if (user) {
        await user.update({
          unsafeMetadata: {
            ...user.unsafeMetadata,
            visitedOnboarding: true,
          },
        });
      }

      reset();
    } catch (error) {
      console.error('Error during onboarding completion:', error);
    }
  };

  if (!isLoaded || (user && !user?.unsafeMetadata?.visitedOnboarding)) {
    return null;
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <img src="/heyAlfie.svg" alt="heyAlfie" className={styles.image} />
      </div>
      <div className={styles.onboarding}>
        <Onboarding onComplete={completeOnboarding} />
      </div>
    </div>
  );
};

export default OnboardingPage;
