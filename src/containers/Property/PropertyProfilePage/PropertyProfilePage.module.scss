.container {
  width: 100%;
  padding: 30px 16px 48px;
  margin: 0 auto;
  box-sizing: border-box;
  height: 100vh;
  max-width: 800px;
  overflow-y: auto;

  @media (max-width: 768px) {
    padding-bottom: 24px;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.content {
  max-width: 798px;
  width: 100%;
  margin: 0 auto;
  overflow: visible;
}

.heading {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 14px;
}

.subheading {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}

.uploadSection {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    padding: 24px 16px;
  }
}

.uploadContent {
  display: flex;
  align-items: center;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.houseIcon {
  flex-shrink: 0;
}

.houseImage {
  width: 120px;
  height: auto;

  @media (max-width: 768px) {
    width: 50px;
  }
}

.uploadText {
  flex: 1;
}

.uploadTitle {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  line-height: 1.4;
  color: #000;

  @media (max-width: 768px) {
    font-size: 16px;
    margin-bottom: 12px;
  }
}

.uploadDescription {
  font-size: 16px;
  line-height: 150%;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 14px;
    margin-bottom: 20px;
  }
}

.uploadingFiles {
  padding: 8px 16px 0 6px;
  overflow-x: auto;
  width: 100%;
  margin-bottom: 16px;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.navigationCards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 16px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.navigationCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
  }

  &:visited {
    color: inherit;
  }

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
}

.cardContent {
  flex: 1;
}

.cardTitle {
  font-size: 16px;
  margin-bottom: 8px;
  color: #000;
}

.cardDescription {
  font-size: 14px;
  line-height: 1.4;
  color: #6b7280;
  margin: 0;
  min-height: 40px;
}

.cardIcon {
  flex-shrink: 0;
  margin-left: 16px;
  color: #000;
  display: flex;
  align-items: center;
  gap: 12px;
}

.circularCountButton {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  min-width: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;

  &:hover {
    pointer-events: none;
  }

  span {
    font-size: 12px !important;
    font-weight: 400 !important;
  }
}
