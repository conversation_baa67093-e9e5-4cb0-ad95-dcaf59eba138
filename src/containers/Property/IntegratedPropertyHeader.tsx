'use client';

import React, { useCallback, useEffect, useState } from 'react';

import { PropertyHeader } from '@/components/PropertyHeader';
import { PropertyHeaderState } from '@/components/PropertyHeader/PropertyHeader.types';
import { useWidgets } from '@/hooks/useWidgets';
import { usePropertyDetailsActions } from '@/hooks/usePropertyDetails';
import { mapUserPropertyRelationTypeToDisplayText } from '@/api/properties';
import useBackendClient from '@/hooks/useBackendClient';
import { AddressFields, AddressType } from '@/components/AddressInput';

interface IntegratedPropertyHeaderProps {
  onPropertyDataChange?: () => void;
}

export const IntegratedPropertyHeader: React.FC<IntegratedPropertyHeaderProps> = ({
  onPropertyDataChange,
}) => {
  const { client } = useBackendClient();
  const { properties, fetchProperties } = useWidgets();
  const { createProperty, updateProperty } = usePropertyDetailsActions();

  const [propertyHeaderState, setPropertyHeaderState] = useState<PropertyHeaderState>(
    PropertyHeaderState.EMPTY
  );
  const [address, setAddress] = useState<string>('');
  const [ownerStatus, setOwnerStatus] = useState<string | undefined>(undefined);
  const [profileImageUrl, setProfileImageUrl] = useState<string>('');

  const refreshPropertyData = useCallback(async () => {
    await fetchProperties(client);
    onPropertyDataChange?.();
  }, [client, fetchProperties, onPropertyDataChange]);

  useEffect(() => {
    void refreshPropertyData();
  }, [refreshPropertyData]);

  useEffect(() => {
    if (properties.length > 0) {
      const currentProperty = properties[0];
      if (!currentProperty.address) return;
      const fullAddress = `${currentProperty.address.streetLine1}, ${currentProperty.address.townOrCity}, ${currentProperty.address.postcode}`;

      setAddress(fullAddress);
      setPropertyHeaderState(PropertyHeaderState.FILLED);

      if (currentProperty.userPropertyRelationshipType) {
        const ownerStatusText = mapUserPropertyRelationTypeToDisplayText(
          currentProperty.userPropertyRelationshipType
        );
        setOwnerStatus(ownerStatusText);
      }
    }
  }, [properties]);

  const handleProfileImageChange = (file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setProfileImageUrl(imageUrl);
  };

  const handleAddressComplete = async (data: { address: AddressType; role: string }) => {
    try {
      if (properties.length === 0) {
        await createProperty({
          address: data.address,
          role: data.role,
        });
      } else {
        await updateProperty(properties[0].id, {
          address: data.address,
          role: data.role,
        });
      }
      await refreshPropertyData();

      setAddress(
        'label' in data.address
          ? data.address.label
          : `${data.address.line1}, ${data.address.city}, ${data.address.postcode}`.trim()
      );

      let ownerStatusText = '';
      switch (data.role) {
        case 'owner':
          ownerStatusText = 'Owner and occupier';
          break;
        case 'landlord':
          ownerStatusText = 'Landlord';
          break;
        case 'tenant':
          ownerStatusText = 'Tenant';
          break;
        default:
          ownerStatusText = data.role;
      }

      setOwnerStatus(ownerStatusText);
      setPropertyHeaderState(PropertyHeaderState.FILLED);
    } catch (error) {
      console.error('Failed to save address:', error);
    }
  };

  return (
    <PropertyHeader
      address={address}
      ownerStatus={ownerStatus}
      profileImageUrl={profileImageUrl}
      state={propertyHeaderState}
      onProfileImageChange={handleProfileImageChange}
      onAddressComplete={handleAddressComplete}
    />
  );
};
