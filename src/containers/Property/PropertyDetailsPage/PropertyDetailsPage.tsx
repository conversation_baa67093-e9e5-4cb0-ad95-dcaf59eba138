'use client';

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useInView } from 'react-intersection-observer';

import { IntegratedPropertyHeader } from '@/containers/Property/IntegratedPropertyHeader';
import { Breadcrumbs } from '@/components/Breadcrumbs';
import { Button } from '@/components/Button';
import { ButtonColor, ButtonState, ButtonType } from '@/components/Button/Button.types';
import { PropertyForm } from '@/components/PropertyForm';
import { PropertyFormData, UserRole } from '@/components/PropertyForm/PropertyForm.types';
import { PersonalizationCard } from '@/components/PersonalizationCard';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
} from '@/components/PersonalizationCard/PersonalizationCard.types';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { useWidgets } from '@/hooks/useWidgets';
import { PropertyDetailsEntry, usePropertyDetailsEntries } from '@/hooks/usePropertyDetailsEntries';
import { UserPropertyRelationType } from '@/api/properties';
import { useFiles } from '@/containers/FilesPage/useFiles';
import styles from './PropertyDetailsPage.module.scss';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';
import useBackendClient from '@/hooks/useBackendClient';

export const PropertyDetailsPage: React.FC = () => {
  const { client } = useBackendClient();
  const { properties, fetchProperties } = useWidgets();
  const {
    entries,
    editingEntries,
    hasMoreItems,
    createEntry,
    updateEntry,
    loadPropertyEntry,
    setEditingEntry,
    cancelEditingEntry,
  } = usePropertyDetailsEntries();

  const entriesHash = JSON.stringify(entries);
  const cachedEntries = useMemo((): PropertyDetailsEntry[] => {
    return JSON.parse(entriesHash);
  }, [entriesHash]);

  const [showManualForm, setShowManualForm] = useState(false);
  const [inlineForm, setInlineForm] = useState<PropertyFormData>({
    userRole: '',
    ownershipType: '',
    propertyType: '',
    propertySubType: '',
    numberOfBedrooms: '',
    numberOfBathrooms: '',
    numberOfFloors: '',
    floorPropertyIsOn: '',
    grossInternalArea: '',
    balconyTerrace: '',
    garden: '',
    swimmingPool: '',
  });
  const entriesContainerRef = useRef<HTMLDivElement>(null);

  const { ref: sentinelRef } = useInView({
    rootMargin: '100px',
    threshold: 0.1,
  });

  const currentPropertyId = properties.length > 0 ? properties[0].id : null;

  const refreshPropertyData = useCallback(async () => {
    await fetchProperties(client);
  }, [client, fetchProperties]);

  useEffect(() => {
    void refreshPropertyData();
  }, [refreshPropertyData]);

  const { uploadFiles, uploadingFiles, deleteNotification, notifications } = useFiles({
    setUploadContext: 'propertyDetails',
    getUploadContext: 'propertyDetails',
    onChanged: refreshPropertyData,
  });

  const firstErrorNotification = notifications.find((n) => n.severity === 'error');
  const firstInfoNotification = notifications.find(
    (n) =>
      n.severity === 'info' &&
      !(n.type === 'information_from_document_saved' && n.category === 'propertyDetails')
  );
  const firstWarningNotification = notifications.find((n) => n.severity === 'warning');

  const propertyItems = [
    'Floor plan',
    'Property survey report',
    'Property brochure or listing',
    'Property check in or inventory report',
  ];

  const handleUploadDocument = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    void uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSaveInlineEntry = async () => {
    if (!currentPropertyId) {
      console.error('Missing property ID');
      return;
    }

    try {
      await createEntry(inlineForm, currentPropertyId, client);

      // Refresh properties data to get updated property with new details
      await refreshPropertyData();

      // Reset form state after successful save
      setInlineForm({
        userRole: '',
        ownershipType: '',
        propertyType: '',
        propertySubType: '',
        numberOfBedrooms: '',
        numberOfBathrooms: '',
        numberOfFloors: '',
        floorPropertyIsOn: '',
        grossInternalArea: '',
        balconyTerrace: '',
        garden: '',
        swimmingPool: '',
      });
      setShowManualForm(false);
    } catch (error) {
      console.error('Failed to save inline property details:', error);
    }
  };

  const handleEdit = (id: string) => () => {
    setEditingEntry(id);
  };

  const handleCancel = (id: string) => () => {
    cancelEditingEntry(id);
  };

  const handleSave =
    (id: string) =>
    async (
      updatedFields: PersonalizationCardField[],
      updatedFiles: PersonalizationCardFile[],
      updatedTitle?: string
    ) => {
      try {
        await updateEntry(id, updatedFields, updatedFiles, updatedTitle, client);
        cancelEditingEntry(id);
      } catch (error) {
        console.error('Failed to update property details entry:', error);
      }
    };

  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (properties.length > 0) {
      const currentProperty = properties[0];

      // Set user role based on existing property data for the inline form
      if (currentProperty.userPropertyRelationshipType) {
        let userRoleValue: UserRole | '' = '';
        switch (currentProperty.userPropertyRelationshipType) {
          case UserPropertyRelationType.OwnerAndOccupier:
            userRoleValue = UserRole.OWNER_AND_OCCUPIER;
            break;
          case UserPropertyRelationType.Landlord:
            userRoleValue = UserRole.LANDLORD;
            break;
          case UserPropertyRelationType.Tenant:
            userRoleValue = UserRole.TENANT;
            break;
        }

        setInlineForm((prevForm) => ({
          ...prevForm,
          userRole: prevForm.userRole || userRoleValue,
        }));
      }

      // Load property data into entries
      loadPropertyEntry(currentProperty);
    }
  }, [properties, loadPropertyEntry]);

  const isFormComplete = () => {
    return (
      inlineForm.userRole.trim() ||
      inlineForm.ownershipType.trim() ||
      inlineForm.propertyType.trim() ||
      inlineForm.propertySubType.trim() ||
      inlineForm.numberOfBedrooms.trim() ||
      inlineForm.numberOfBathrooms.trim() ||
      inlineForm.numberOfFloors.trim() ||
      inlineForm.floorPropertyIsOn.trim() ||
      inlineForm.grossInternalArea.trim() ||
      inlineForm.balconyTerrace.trim() ||
      inlineForm.garden.trim() ||
      inlineForm.swimmingPool.trim()
    );
  };

  // Check if property has minimal data and should show the form
  const shouldShowInlineForm = () => {
    if (properties.length === 0) return false;
    if (showManualForm) return true;

    // If we have a property, check if it has actual details beyond just address
    if (properties.length > 0) {
      const currentProperty = properties[0];
      const hasDetails =
        currentProperty.userPropertyRelationshipType ||
        currentProperty.type ||
        currentProperty.numberOfBedrooms ||
        currentProperty.numberOfBathrooms ||
        currentProperty.numberOfFloors ||
        currentProperty.sizeInSqft ||
        currentProperty.hasBalconyTerrace !== null ||
        currentProperty.hasGarden !== null ||
        currentProperty.hasSwimmingPool !== null ||
        currentProperty.onFloorLevel ||
        currentProperty.tenureType ||
        currentProperty.subgroupType;

      // If has details, don't show inline form (show PersonalizationCard instead)
      // If no details, show inline form
      return !hasDetails;
    }

    return entries.length === 0;
  };

  // Check if we should show PersonalizationCard entries
  const shouldShowPersonalizationCard = () => {
    if (properties.length === 0) return false;

    const currentProperty = properties[0];
    const hasDetails =
      currentProperty.userPropertyRelationshipType ||
      currentProperty.type ||
      currentProperty.numberOfBedrooms ||
      currentProperty.numberOfBathrooms ||
      currentProperty.numberOfFloors ||
      currentProperty.sizeInSqft ||
      currentProperty.hasBalconyTerrace !== null ||
      currentProperty.hasGarden !== null ||
      currentProperty.hasSwimmingPool !== null ||
      currentProperty.onFloorLevel ||
      currentProperty.tenureType ||
      currentProperty.subgroupType;

    return hasDetails;
  };

  return (
    <div className={styles.container}>
      <Breadcrumbs
        path={[
          { value: 'Property Profile', url: '/property-profile' },
          { value: 'Property Details' },
        ]}
      />
      <div className={styles.content}>
        <IntegratedPropertyHeader onPropertyDataChange={refreshPropertyData} />
      </div>
      <h1 className={styles.heading}>Property Details</h1>
      <div className={styles.content}>
        <h2 className={styles.subheading}>Upload documents</h2>
        <p className={styles.paragraph}>
          Provide any document that describes the layout and features of the property. Examples:
        </p>
        <ul className={styles.list}>
          {propertyItems.map((item, index) => (
            <li key={index} className={styles.listItem}>
              {item}
            </li>
          ))}
        </ul>
        <p className={styles.paragraph}>
          Uploading more documents will give Alfie more context and better personalised assistance.
        </p>

        <div className={styles.buttons}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            onClick={handleUploadDocument}
            state={properties.length === 0 ? ButtonState.DISABLED : ButtonState.DEFAULT}
            disabled={properties.length === 0}
          >
            Upload documents
          </Button>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.png,.jpg,.jpeg,.webp,.gif,.heic,.heif"
          multiple
          style={{ display: 'none' }}
        />

        {uploadingFiles.length > 0 && (
          <>
            <h2 className={styles.subheading}>Your documents</h2>
            <div className={styles.uploadingFiles}>
              <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
            </div>
          </>
        )}

        <div className={styles.alerts}>
          {firstErrorNotification && (
            <NotificationAlert notification={firstErrorNotification} onClose={deleteNotification} />
          )}
          {firstWarningNotification && (
            <NotificationAlert
              notification={firstWarningNotification}
              onClose={deleteNotification}
            />
          )}
          {firstInfoNotification && (
            <NotificationAlert notification={firstInfoNotification} onClose={deleteNotification} />
          )}
        </div>

        {shouldShowInlineForm() && (
          <>
            <h3 className={styles.inlineFormTitle}>Add Property Details</h3>
            <PropertyForm value={inlineForm} onChange={setInlineForm} showUploadButton={false} />
            <div className={styles.inlineFormActions}>
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.GREEN_PRIMARY}
                state={isFormComplete() ? ButtonState.DEFAULT : ButtonState.DISABLED}
                onClick={handleSaveInlineEntry}
                disabled={!isFormComplete()}
              >
                Save
              </Button>
            </div>
          </>
        )}
      </div>

      <div className={styles.content} ref={entriesContainerRef}>
        {shouldShowPersonalizationCard() &&
          cachedEntries.map((entry) => (
            <div key={entry.id} className={styles.PersonalizationCard}>
              <PersonalizationCard
                title="Property Details"
                titleEditable={false}
                fields={entry.fields}
                files={entry.files}
                isEditing={editingEntries.includes(entry.id)}
                onEdit={handleEdit(entry.id)}
                onCancel={handleCancel(entry.id)}
                onSave={handleSave(entry.id)}
                context="propertyDetails"
              />
            </div>
          ))}
        {hasMoreItems && entries.length > 0 && <div ref={sentinelRef} style={{ height: 50 }} />}
      </div>
    </div>
  );
};
