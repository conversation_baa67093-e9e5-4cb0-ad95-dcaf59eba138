.container {
  width: 100%;
  padding: 0 16px 48px;
  margin: 0 auto;
  box-sizing: border-box;
  height: 100vh;
  max-width: 798px;
  overflow-y: auto;

  @media (max-width: 768px) {
    padding-bottom: 24px;
  }

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.content {
  max-width: 798px;
  width: 100%;
  margin: 0 auto;
  overflow: visible;
}

.heading {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 14px;
}

.subheading {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.paragraph {
  font-size: 16px;
}

.list {
  margin: 0 24px;
  padding: 0;
  list-style-type: disc;
}

.listItem {
  margin-bottom: 2px;
  font-size: 16px;
  padding-left: 8px;
}

.PersonalizationCard {
  margin-top: 24px;
  max-width: 100%;
  overflow: visible;
}

.buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 16px;
}

.uploadingFiles {
  padding: 8px 16px 0 6px;
  overflow-x: auto;
  width: 100%;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
  margin-bottom: 16px;
}

.fileGrid {
  width: 100%;
}

.divider {

  &::before,
  &::after {
    content: '';
    display: block;
    height: 30px;
  }

  &::after {
    height: 30px;
  }

  &::before {
    height: 14px;
  }

  &::after {
    border-top: 1px solid var(--colors-gray-200);
  }

  @media (max-width: 768px) {
    &::before {
      height: 0;
    }

    &::after {
      border-top: 0;
      height: 15px;
    }
  }
}

.inlineFormTitle {
  font-size: 20px;
  font-weight: bold;
  color: var(--colors-black);
  margin-bottom: 20px;
}

.inlineFormActions {
  display: flex;
}

.loadingSpinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  margin-top: 16px;
}