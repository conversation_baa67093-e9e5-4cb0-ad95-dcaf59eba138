'use client';

import { useEffect } from 'react';
import { useAuthStore } from '~/stores/auth.store';

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const setAuth = useAuthStore((state) => state.setAuth);

  useEffect(() => {
    // This will be replaced with Clerk's useUser() hook later
    const checkAuth = async () => {
      try {
        // Simulated auth check - replace with <PERSON> logic later
        const isAuthenticated = false;
        const user = null;
        setAuth(isAuthenticated, user);
      } catch (error) {
        console.error('Auth check failed:', error);
        setAuth(false, null);
      }
    };

    checkAuth();
  }, [setAuth]);

  return <>{children}</>;
}
