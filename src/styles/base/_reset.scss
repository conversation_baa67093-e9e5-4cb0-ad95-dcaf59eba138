// Reset default styles
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: theme-color('default', 'background', 'primary');
  color: theme-color('default', 'text', 'primary');
  font-family: 'Quasimoda', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Yellow theme
.yellow-theme {
  background-color: theme-color('yellow', 'background', 'primary');
  color: theme-color('yellow', 'text', 'primary');
}
