@use './abstracts/tokens';
@use './abstracts/variables';
@use './abstracts/mixins';

// Global styles for Storybook
.storybook-container {
  @include mixins.body;
  padding: tokens.spacing('4');
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 100vw;

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
}

// Typography preview styles
.typography-preview {
  display: flex;
  flex-direction: column;
  gap: tokens.spacing('8');
  min-width: 400px;

  &__item {
    display: flex;
    flex-direction: column;
    gap: tokens.spacing('2');
  }

  &__meta {
    @include mixins.body-s;
    color: tokens.color('dark', '20');
  }
}
