@use 'abstracts/tokens';
@use 'abstracts/variables';
@use 'abstracts/functions';
@use 'abstracts/mixins';

@use 'base/reset';
@use 'base/typography';

@use 'themes/default';

@import 'tailwindcss';

:root {
  --font-primary: #{inspect(map-get(tokens.$font-families, 'primary'))};
  --sidebar-width: 280px;
  --navbar-height: 64px;
}

.modal-overlay {
  z-index: 9999 !important;
}

.modal-content {
  z-index: 10000 !important;
}

[data-radix-dialog-overlay] {
  z-index: 10002 !important;
}

[data-radix-dialog-content] {
  z-index: 10003 !important;
}

.modal,
.modal-overlay,
.modal-content,
[data-radix-dialog-overlay],
[data-radix-dialog-content] {
  isolation: isolate;
}

.UserInfoModal .PersonalizationCard {
  z-index: 10001 !important;
}

.UserInfoModal .PersonalizationCard * {
  z-index: 10001 !important;
}

// Modal scroll blocking
body.modal-open {
  overflow: hidden !important;
}

.app-container {
  display: grid;
  min-height: 100dvh;
  grid-template-columns: auto 1fr;
  grid-template-rows: auto 1fr;
  grid-template-areas:
    'sidebar header'
    'sidebar main';
}

.header {
  grid-area: header;
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-color);
}

.sidebar {
  grid-area: sidebar;
  background: var(--background-secondary);
  border-right: 1px solid var(--border-color);
  overflow: hidden;
}

.content {
  grid-area: main;
  overflow: auto;
}
