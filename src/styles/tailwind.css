:root {
  --border-radius-rounded: 4px;
  --border-radius-rounded-2xl: 16px;
  --border-radius-rounded-3xl: 24px;
  --border-radius-rounded-full: 9999px;
  --border-radius-rounded-lg: 8px;
  --border-radius-rounded-md: 6px;
  --border-radius-rounded-sm: 2px;
  --border-radius-rounded-xl: 12px;
  --colors-black: #000000;
  --colors-blue-100: #A8DCFF;
  --colors-blue-200: #88CEFD;
  --colors-blue-300: #5EAFE6;
  --colors-blue-400: #418CBE;
  --colors-blue-50: #C8E9FF;
  --colors-blue-500: #31709B;
  --colors-blue-600: #245F88;
  --colors-blue-700: #21587E;
  --colors-blue-800: #0E3B5A;
  --colors-blue-900: #022034;
  --colors-blue-1000: #141B34;
  --colors-gray-100: #F3F4F6;
  --colors-gray-200: #E5E7EB;
  --colors-gray-300: #D1D5DB;
  --colors-gray-400: #9CA3AF;
  --colors-gray-50: #F9FAFB;
  --colors-gray-500: #6B7280;
  --colors-gray-600: #4B5563;
  --colors-gray-700: #374151;
  --colors-gray-800: #1F2A37;
  --colors-gray-900: #111928;
  --colors-green-100: rgba(185, 253, 236, 1);
  --colors-green-200: rgba(136, 249, 222, 1);
  --colors-green-300: rgba(90, 237, 201, 1);
  --colors-green-400: rgba(63, 191, 160, 1);
  --colors-green-50: rgba(225, 255, 248, 1);
  --colors-green-500: rgba(62, 150, 129, 1);
  --colors-green-600: rgba(32, 128, 105, 1);
  --colors-green-700: rgba(27, 110, 90, 1);
  --colors-green-800: rgba(20, 85, 67, 1);
  --colors-green-900: rgba(14, 55, 44, 1);
  --colors-indigo-100: rgba(229, 237, 255, 1);
  --colors-indigo-200: rgba(205, 219, 254, 1);
  --colors-indigo-300: rgba(180, 198, 252, 1);
  --colors-indigo-400: rgba(141, 162, 251, 1);
  --colors-indigo-50: rgba(240, 245, 255, 1);
  --colors-indigo-500: rgba(104, 117, 245, 1);
  --colors-indigo-600: rgba(88, 80, 236, 1);
  --colors-indigo-700: rgba(81, 69, 205, 1);
  --colors-indigo-800: rgba(66, 56, 157, 1);
  --colors-indigo-900: rgba(54, 47, 120, 1);
  --colors-neutral-100: rgba(240, 232, 220, 1);
  --colors-neutral-200: rgba(223, 216, 203, 1);
  --colors-neutral-300: rgba(195, 187, 172, 1);
  --colors-neutral-400: rgba(148, 144, 135, 1);
  --colors-neutral-50: rgba(248, 243, 233, 1);
  --colors-neutral-500: rgba(143, 136, 132, 1);
  --colors-neutral-600: rgba(117, 107, 102, 1);
  --colors-neutral-700: rgba(97, 88, 83, 1);
  --colors-neutral-800: rgba(74, 64, 58, 1);
  --colors-neutral-900: rgba(52, 43, 37, 1);
  --colors-orange-100: rgba(252, 199, 171, 1);
  --colors-orange-200: rgba(244, 177, 143, 1);
  --colors-orange-300: rgba(233, 140, 92, 1);
  --colors-orange-400: rgba(214, 100, 40, 1);
  --colors-orange-50: rgba(255, 218, 199, 1);
  --colors-orange-500: rgba(190, 90, 38, 1);
  --colors-orange-600: rgba(163, 78, 34, 1);
  --colors-orange-700: rgba(136, 64, 27, 1);
  --colors-orange-800: rgba(107, 49, 19, 1);
  --colors-orange-900: rgba(75, 35, 14, 1);
  --colors-pink-100: rgba(252, 232, 243, 1);
  --colors-pink-200: rgba(250, 209, 232, 1);
  --colors-pink-300: rgba(248, 180, 217, 1);
  --colors-pink-400: rgba(241, 126, 184, 1);
  --colors-pink-50: rgba(253, 242, 248, 1);
  --colors-pink-500: rgba(231, 70, 148, 1);
  --colors-pink-600: rgba(214, 31, 105, 1);
  --colors-pink-700: rgba(191, 18, 93, 1);
  --colors-pink-800: rgba(153, 21, 75, 1);
  --colors-pink-900: rgba(117, 26, 61, 1);
  --colors-purple-100: rgba(235, 215, 255, 1);
  --colors-purple-200: rgba(217, 182, 255, 1);
  --colors-purple-300: rgba(198, 144, 255, 1);
  --colors-purple-400: rgba(170, 105, 238, 1);
  --colors-purple-50: rgba(244, 233, 255, 1);
  --colors-purple-500: rgba(143, 82, 207, 1);
  --colors-purple-600: rgba(101, 58, 146, 1);
  --colors-purple-700: rgba(75, 42, 109, 1);
  --colors-purple-800: rgba(52, 29, 76, 1);
  --colors-purple-900: rgba(32, 8, 56, 1);
  --colors-red-100: rgba(255, 206, 218, 1);
  --colors-red-200: rgba(235, 159, 177, 1);
  --colors-red-300: rgba(215, 116, 140, 1);
  --colors-red-400: rgba(175, 50, 80, 1);
  --colors-red-50: rgba(255, 224, 231, 1);
  --colors-red-500: rgba(157, 40, 68, 1);
  --colors-red-600: rgba(140, 33, 59, 1);
  --colors-red-700: rgba(125, 35, 57, 1);
  --colors-red-800: rgba(101, 31, 48, 1);
  --colors-red-900: rgba(48, 4, 14, 1);
  --colors-teal-100: rgba(213, 245, 246, 1);
  --colors-teal-200: rgba(175, 236, 239, 1);
  --colors-teal-300: rgba(126, 220, 226, 1);
  --colors-teal-400: rgba(22, 189, 202, 1);
  --colors-teal-50: rgba(237, 250, 250, 1);
  --colors-teal-500: rgba(6, 148, 162, 1);
  --colors-teal-600: rgba(4, 116, 129, 1);
  --colors-teal-700: rgba(3, 102, 114, 1);
  --colors-teal-800: rgba(5, 80, 92, 1);
  --colors-teal-900: rgba(1, 68, 81, 1);
  --colors-white: #FFFFFF;
  --colors-yellow-100: rgba(255, 226, 182, 1);
  --colors-yellow-200: rgba(255, 210, 142, 1);
  --colors-yellow-300: rgba(247, 188, 100, 1);
  --colors-yellow-400: rgba(227, 161, 62, 1);
  --colors-yellow-50: rgba(255, 235, 206, 1);
  --colors-yellow-500: rgba(210, 142, 40, 1);
  --colors-yellow-600: rgba(187, 123, 28, 1);
  --colors-yellow-700: rgba(162, 106, 23, 1);
  --colors-yellow-800: rgba(138, 88, 13, 1);
  --colors-yellow-900: rgba(113, 70, 6, 1);
  --spacing-0: 0px;
  --spacing-px: 1px;
  --spacing-0-5: 2px;
  --spacing-1: 4px;
  --spacing-1-5: 6px;
  --spacing-2: 8px;
  --spacing-2-5: 10px;
  --spacing-3: 12px;
  --spacing-3-5: 14px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-7: 28px;
  --spacing-8: 32px;
  --spacing-9: 36px;
  --spacing-10: 40px;
  --spacing-11: 44px;
  --spacing-12: 48px;
  --spacing-14: 56px;
  --spacing-16: 64px;
  --spacing-20: 80px;
  --spacing-24: 96px;
  --spacing-28: 112px;
  --spacing-32: 128px;
  --spacing-36: 144px;
  --spacing-40: 160px;
  --spacing-44: 176px;
  --spacing-48: 192px;
  --spacing-52: 208px;
  --spacing-56: 224px;
  --spacing-60: 240px;
  --spacing-64: 256px;
  --spacing-72: 288px;
  --spacing-80: 320px;
  --spacing-96: 384px;
}
