@use 'tokens';
@use 'variables' as vars;
@use 'sass:map';

// Responsive breakpoints mixin
@mixin respond-to($breakpoint) {
  @if map.has-key(tokens.$breakpoints, $breakpoint) {
    @media screen and (min-width: map.get(tokens.$breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin font-primary {
  font-family: vars.$font-family-primary;
}

@mixin font-heading {
  font-family: vars.$font-family-heading;
}

// Typography Presets
@mixin h1 {
  font-weight: 900;
  line-height: map.get(tokens.$line-heights, 'h1');
  font-size: map.get(tokens.$font-sizes, 'h1-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'h1-desktop');
  }
}

@mixin h2 {
  font-weight: 700;
  line-height: map.get(tokens.$line-heights, 'h2');
  font-size: map.get(tokens.$font-sizes, 'h2-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'h2-desktop');
  }
}

@mixin h3 {
  font-weight: 700;
  line-height: map.get(tokens.$line-heights, 'h3');
  font-size: map.get(tokens.$font-sizes, 'h3-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'h3-desktop');
  }
}

@mixin h4 {
  font-weight: 700;
  line-height: map.get(tokens.$line-heights, 'h4');
  font-size: map.get(tokens.$font-sizes, 'h4-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'h4-desktop');
  }
}

@mixin h5 {
  font-weight: 700;
  line-height: map.get(tokens.$line-heights, 'h5');
  font-size: map.get(tokens.$font-sizes, 'h5-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'h5-desktop');
  }
}

@mixin h6 {
  font-weight: 700;
  line-height: map.get(tokens.$line-heights, 'h6');
  font-size: map.get(tokens.$font-sizes, 'h6-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'h6-desktop');
  }
}

@mixin body-l {
  line-height: map.get(tokens.$line-heights, 'body');
  font-size: map.get(tokens.$font-sizes, 'body-l-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'body-l');
  }
}

@mixin body {
  line-height: map.get(tokens.$line-heights, 'body');
  font-size: map.get(tokens.$font-sizes, 'body-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'body');
  }
}

@mixin body-s {
  line-height: map.get(tokens.$line-heights, 'body');
  font-size: map.get(tokens.$font-sizes, 'body-s-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'body-s');
  }
}

@mixin body-xs {
  line-height: map.get(tokens.$line-heights, 'body');
  font-size: map.get(tokens.$font-sizes, 'body-s-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'body-xs');
  }
}

@mixin body-xss {
  line-height: map.get(tokens.$line-heights, 'body');
  font-size: map.get(tokens.$font-sizes, 'body-s-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'body-xss');
  }
}

@mixin button-text {
  line-height: map.get(tokens.$line-heights, 'button');
  font-size: map.get(tokens.$font-sizes, 'button-mobile');
  
  @include respond-to('md') {
    font-size: map.get(tokens.$font-sizes, 'button-desktop');
  }
} 