import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  email: string;
  name?: string;
}

interface GuestAuth {
  token: string;
  userId: number;
  expiresAt: string; // ISO string
}

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  guestAuth: GuestAuth | null;
  setAuth: (isAuthenticated: boolean, user: User | null) => void;
  logout: () => void;
  resetStore: () => void;
  setToken: (token: string) => void;
  setGuestAuth: (guestAuth: GuestAuth) => void;
  clearGuestAuth: () => void;
  isGuestTokenValid: () => boolean;
  getValidGuestToken: () => string | null;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      token: null,
      guestAuth: null,
      setAuth: (isAuthenticated, user) => set({ isAuthenticated, user }),
      setToken: (token) => set({ token }),
      logout: () =>
        set({
          isAuthenticated: false,
          user: null,
          guestAuth: null,
        }),
      resetStore: () => {
        set({
          isAuthenticated: false,
          user: null,
          token: null,
          guestAuth: null,
        });
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth-storage');
        }
      },
      setGuestAuth: (guestAuth) => set({ guestAuth }),
      clearGuestAuth: () => set({ guestAuth: null }),
      isGuestTokenValid: () => {
        const { guestAuth } = get();
        if (!guestAuth) return false;

        const now = new Date();
        const expiresAt = new Date(guestAuth.expiresAt);
        return now < expiresAt;
      },
      getValidGuestToken: () => {
        const { guestAuth, isGuestTokenValid } = get();
        if (!guestAuth || !isGuestTokenValid()) {
          return null;
        }
        return guestAuth.token;
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        guestAuth: state.guestAuth,
      }),
    }
  )
);
