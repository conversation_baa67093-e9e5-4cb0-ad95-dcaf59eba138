import { DocumentCategoryType } from '@/api/notifications';

export type FileStatus = 'uploading' | 'success' | 'error';
export type FileType = 'image' | 'document' | 'pdf';

export interface BaseFile {
  id: string;
  name: string;
  type: FileType;
  status: FileStatus;
  createdAt: string;
  browserMimeType: string;
  sizeInKiloBytes: number;
}

export interface LocalFile extends BaseFile {
  blob: Blob;
}

export interface UploadedFile extends BaseFile {
  documentId: number;
}

export interface CategorizedFile extends UploadedFile {
  category: DocumentCategoryType;
  label?: string;
}

export type FetchedFile = UploadedFile | CategorizedFile;

export type UniversalFile = CategorizedFile | UploadedFile | LocalFile;

export const isUploadedFile = (file: UniversalFile): file is UploadedFile => {
  return 'documentId' in file && typeof file.documentId === 'number';
};

export const isCategorizedFile = (file: UniversalFile): file is CategorizedFile => {
  return (
    'category' in file &&
    typeof (file as UploadedFile & { category?: string }).category === 'string' &&
    (file as UploadedFile & { category?: string }).category !== null &&
    (file as UploadedFile & { category?: string }).category !== undefined
  );
};
