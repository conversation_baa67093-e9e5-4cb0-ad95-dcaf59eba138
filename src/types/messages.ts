import { BaseFile, UploadedFile } from './file';
import { DocumentDto } from '@/api/documents';

export type MessageType = 'text' | 'image' | 'diagnostic_report';
export type SenderType = 'user' | 'system' | 'customer_support';

export interface ResponseMessageMetadata {
  timestamp: Date;
  category?: string;
  confidence?: number;
}

export interface ResponseMessage {
  content: string;
  type: MessageType;
  additionalData?: ResponseMessageMetadata;
}

export interface SendMessageResponse {
  chatId: number;
  userMessageId: number;
  systemMessageId: number;
}

export interface CompleteSendMessageResponse extends SendMessageResponse {
  message: ResponseMessage;
  additionalData?: MessageAdditionalData;
}

export interface ImageUrl {
  imageUrl: string;
  description: string;
  source: string;
}

export interface MessageAdditionalData {
  imageUrl?: string;
  category?: string;
  device?: string;
  location?: string;
  timestamp?: string;
  confidence?: number;
  suggestedActions?: {
    type: string;
    label: string;
    action: string;
  }[];
  imageClickableUrls?: ImageUrl[];
  imageUrls?: ImageUrl[];
  jobSummary?: {
    jobId: string;
    jobCategory: string;
    jobSubCategory: string;
    jobHeadline: string;
    jobDetails: string;
    jobDate: string;
    jobTimeOfDay: string;
    messageContainingJobSummary: string;
    errorDuringParsing: string;
  };
}

export interface Message {
  id: number;
  content: string;
  type: MessageTypeValue;
  senderType: SenderType;
  timestamp: string;
  additionalData?: MessageAdditionalData;
  isOptimistic?: boolean;
  documents?: DocumentDto[];
}

export interface MessageListResponse {
  items: Message[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export enum MessageTypeValue {
  Text = 'text',
  Image = 'image',
}
