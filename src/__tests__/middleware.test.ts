import { NextRequest } from 'next/server';
import middleware, { config } from '../middleware';
import type { Mock } from 'vitest';

type AuthObject = {
  protect: () => void;
};

vi.mock('@clerk/nextjs/server', () => ({
  clerkMiddleware: vi.fn((fn) => fn),
  createRouteMatcher: vi.fn((routes: string[]) => (req: NextRequest) => {
    const url = new URL(req.url);
    return routes.some((route) => url.pathname === route);
  })
}));

describe('Middleware', () => {
  let mockAuth: { protect: Mock };
  let mockReq: NextRequest;

  beforeEach(() => {
    mockAuth = { protect: vi.fn() };
    mockReq = new NextRequest(new Request('http://localhost:3000/test'));
  });

  it('protects non-public routes', async () => {
    mockReq = new NextRequest(new Request('http://localhost:3000/dashboard'));
    await middleware(mockAuth as AuthObject, mockReq);
    expect(mockAuth.protect).toHaveBeenCalled();
  });

  it('allows access to public routes without protection', async () => {
    mockReq = new NextRequest(new Request('http://localhost:3000/'));
    await middleware(mockAuth as AuthObject, mockReq);
    expect(mockAuth.protect).not.toHaveBeenCalled();
  });

  describe('config', () => {
    it('has correct matcher patterns', () => {
      expect(config.matcher).toEqual([
        '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
        '/(api|trpc)(.*)'
      ]);
    });

    it('excludes static files and includes API routes', () => {
      const [mainPattern, apiPattern] = config.matcher;
      
      expect('/_next/static/file.js').not.toMatch(new RegExp(mainPattern));
      expect('/styles.css').not.toMatch(new RegExp(mainPattern));
      
      expect('/api/test').toMatch(new RegExp(apiPattern));
      expect('/trpc/query').toMatch(new RegExp(apiPattern));
    });
  });
}); 