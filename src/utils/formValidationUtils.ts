/**
 * Shared form validation utilities
 */

export const isEmpty = (value: string | number | undefined | null): boolean => {
  return value === undefined || value === null || String(value).trim() === '';
};

export const isValidEmail = (email: string): boolean => {
  return /^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email);
};

export const isValidPhoneNumber = (phone: string): boolean => {
  const cleanPhone = phone.replace(/\D/g, '');
  return cleanPhone.length >= 10;
};

export const validateRequiredField = (
  value: string | number | undefined | null,
  fieldName: string
): string | null => {
  if (isEmpty(value)) {
    return `${fieldName} is required`;
  }
  return null;
};

export const validateEmailField = (email: string): string | null => {
  if (isEmpty(email)) {
    return 'Email is required';
  }
  if (!isValidEmail(email)) {
    return 'Please enter a valid email address';
  }
  return null;
};

export const validatePhoneField = (phone: string): string | null => {
  if (isEmpty(phone)) {
    return 'Phone number is required';
  }
  if (!isValidPhoneNumber(phone)) {
    return 'Please enter a valid phone number';
  }
  return null;
};

export const formatPhoneNumber = (input: string): string => {
  let formatted = input.replace(/[^\d+]/g, '');

  if (formatted.startsWith('+')) {
    formatted = '+' + formatted.substring(1).replace(/\+/g, '');
  }

  if (formatted.length > 0 && !formatted.startsWith('+')) {
    formatted = '+' + formatted;
  }

  return formatted;
};

export const validateFieldValue = (fieldType: string, value: string | number): string | number => {
  if (fieldType === 'number' && typeof value === 'string') {
    const numValue = parseFloat(value);
    return isNaN(numValue) ? '' : numValue;
  }
  return value;
};

export const areAllFieldsValid = (
  fields: Array<{ value: string | number; required?: boolean }>
): boolean => {
  return fields.filter((field) => field.required !== false).every((field) => !isEmpty(field.value));
};

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export const validateForm = (
  fields: Record<
    string,
    { value: string | number; validators: ((value: string | number) => string | null)[] }
  >
): ValidationResult => {
  const errors: Record<string, string> = {};

  Object.entries(fields).forEach(([fieldName, { value, validators }]) => {
    for (const validator of validators) {
      const error = validator(value);
      if (error) {
        errors[fieldName] = error;
        break;
      }
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
