// GTM and Analytics utilities

/**
 * Type definitions for GTM data layer
 */
export interface PageViewEvent {
  event: 'page_view';
  page_path: string;
  page_title: string;
}

export interface CustomEvent {
  event: string;
  [key: string]: unknown;
}

export type DataLayerEvent = PageViewEvent | CustomEvent;

// Define interface for window with dataLayer
declare global {
  interface Window {
    dataLayer?: unknown[];
  }
}

/**
 * Check if we're in production environment
 */
export const getIsProduction = (): boolean => {
  return process.env.NEXT_PUBLIC_VERCEL_ENV === 'production';
};

/**
 * Initialize the data layer array if it doesn't exist yet
 */
export const initDataLayer = (): void => {
  if (!getIsProduction()) return;
  window.dataLayer = window.dataLayer || [];
};

/**
 * Push an event to the data layer
 */
export const pushEvent = (event: DataLayerEvent): void => {
  if (typeof window !== 'undefined' && window.dataLayer && getIsProduction()) {
    window.dataLayer.push(event);
  }
};

/**
 * Track a page view
 */
export const trackPageView = (path: string, title: string): void => {
  pushEvent({
    event: 'page_view',
    page_path: path,
    page_title: title,
  });
};

/**
 * Track a custom event
 */
export const trackEvent = (eventName: string, eventProps: Record<string, unknown> = {}): void => {
  pushEvent({
    event: eventName,
    ...eventProps,
  });
}; 