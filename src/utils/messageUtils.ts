import { DocumentDto } from '@/api/documents';
import { FetchedFile, UniversalFile, UploadedFile } from '@/types/file';
import { getFileType } from '@/utils/fileUtils';

export interface MessageButton {
  text: string;
  value: string;
}

export const extractButtons = (content: string): MessageButton[] => {
  const buttons: MessageButton[] = [];

  const buttonRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  let match;
  while ((match = buttonRegex.exec(content)) !== null) {
    buttons.push({
      text: match[1],
      value: match[2],
    });
  }

  const optionsRegex = /\[Options:\s*([^\]]+)\]/g;
  while ((match = optionsRegex.exec(content)) !== null) {
    const options = match[1].split(';').map((opt) => opt.trim());
    options.forEach((option) => {
      buttons.push({
        text: option,
        value: option,
      });
    });
  }

  return buttons;
};

export const stripButtonsFromText = (content: string): string => {
  return content.replace(/\[Options:[^\]]+\]/g, '');
};

/**
 * Filters an array of image URLs to return only those that can be successfully loaded
 * @param imageUrls Array of image URLs to check
 * @returns Promise that resolves to an array of valid image URLs
 */
export const filterValidImages = async (imageUrls: string[]): Promise<string[]> => {
  if (!imageUrls || imageUrls.length === 0) {
    return [];
  }

  const checkImage = (url: string): Promise<string | null> => {
    return new Promise((resolve) => {
      const img = new Image();

      img.onload = () => {
        resolve(url);
      };

      img.onerror = () => {
        console.warn(`Image failed to load: ${url}`);
        resolve(null);
      };

      const timeoutId = setTimeout(() => {
        img.src = '';
        console.warn(`Image load timed out: ${url}`);
        resolve(null);
      }, 5000);

      img.src = url;

      img.onload = () => {
        clearTimeout(timeoutId);
        resolve(url);
      };
    });
  };

  const results = await Promise.all(imageUrls.map(checkImage));
  return results.filter((url): url is string => url !== null);
};

export const mapFetchedFilesToDocuments = (files?: FetchedFile[]): DocumentDto[] => {
  if (!files) {
    return [];
  }

  return files.map((file): DocumentDto => ({
    id: file.documentId,
    fileName: file.name,
    sizeInKiloBytes: file.sizeInKiloBytes,
    browserMimeType: file.browserMimeType,
    createdAt: file.createdAt || new Date().toISOString(),
    uploadContext: 'chat',
    status: 'saved',
    label: 'label' in file ? file.label : undefined,
    category: 'category' in file ? file.category : undefined,
  }));
};

export const mapDocumentsToFetchedFile = (dtos?: DocumentDto[]): FetchedFile[] => {
  if (!dtos) {
    return [];
  }

  return dtos.map((dto): FetchedFile => {
    const documentId = dto.id;
    const uploadedFile: UploadedFile = {
      id: String(documentId),
      documentId: documentId,
      name: dto.fileName,
      type: getFileType(dto.fileName, dto.browserMimeType),
      browserMimeType: dto.browserMimeType,
      status:
        dto.status === 'processingCompleted'
          ? 'success'
          : (dto.status === 'error' || dto.status === 'irrelevant')
            ? 'error'
            : 'uploading',
      createdAt: dto.createdAt,
      sizeInKiloBytes: dto.sizeInKiloBytes,
    };
    if (dto.category) {
      return {
        ...uploadedFile,
        category: dto.category,
        label: dto.label || undefined,
      };
    }
    return uploadedFile;
  });
};