import { UniversalFile, FileType, isUploadedFile } from '@/types/file';

export const SUPPORTED_FILE_TYPES = {
  documents: ['.pdf', '.docx', '.doc', '.txt'],
  images: ['.jpg', '.jpeg', '.png', '.heic', '.heif'],
  spreadsheets: ['.xls', '.xlsx', '.csv'],
} as const;

export const MIME_TYPE_MAP = {
  'application/pdf': '.pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/msword': '.doc',
  'text/plain': '.txt',
  'image/jpeg': '.jpg',
  'image/jpg': '.jpg',
  'image/png': '.png',
  'image/heic': '.heic',
  'image/heif': '.heif',
  'application/vnd.ms-excel': '.xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
  'text/csv': '.csv',
} as const;

export const ALL_SUPPORTED_EXTENSIONS = Object.values(SUPPORTED_FILE_TYPES).flat();
export const ALL_SUPPORTED_TYPES = ALL_SUPPORTED_EXTENSIONS.join(',');

export { isUploadedFile };

export const isValidFile = (file: File): boolean => {
  const extension = `.${file.name.split('.').pop()?.toLowerCase()}`;
  const mimeExtension = MIME_TYPE_MAP[file.type as keyof typeof MIME_TYPE_MAP];
  return (
    ALL_SUPPORTED_EXTENSIONS.includes(extension as (typeof ALL_SUPPORTED_EXTENSIONS)[number]) ||
    (mimeExtension && ALL_SUPPORTED_EXTENSIONS.includes(mimeExtension))
  );
};

export const getFileType = (fileName: string, mimeType: string): FileType => {
  if (mimeType.includes('pdf') || fileName.toLowerCase().endsWith('.pdf')) return 'pdf';
  if (mimeType.includes('image')) return 'image';
  const extension = `.${fileName.split('.').pop()?.toLowerCase()}`;
  if (
    SUPPORTED_FILE_TYPES.images.includes(extension as (typeof SUPPORTED_FILE_TYPES.images)[number])
  )
    return 'image';
  return 'document';
};

export const getFileId = (file: UniversalFile): string | number => {
  if (isUploadedFile(file)) {
    return file.documentId;
  }
  return file.id;
};
