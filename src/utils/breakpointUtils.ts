import { useState, useEffect } from 'react';

export type Breakpoint = 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export const breakpoints: Record<Breakpoint, number> = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

export const useBreakpoint = () => {
  const [windowWidth, setWindowWidth] = useState<number>(
    typeof window !== 'undefined' ? window.innerWidth : 0
  );

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize);
      handleResize();
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  const getIsMobile = (breakpoint: Breakpoint = 'md'): boolean => {
    return windowWidth < breakpoints[breakpoint];
  };

  const getIsDesktop = (breakpoint: Breakpoint = 'md'): boolean => {
    return windowWidth >= breakpoints[breakpoint];
  };

  const isBreakpointBetween = (
    lowerBreakpoint: Breakpoint,
    upperBreakpoint: Breakpoint
  ): boolean => {
    return (
      windowWidth >= breakpoints[lowerBreakpoint] &&
      windowWidth <= breakpoints[upperBreakpoint]
    );
  };

  const matchesBreakpoint = (breakpoint: Breakpoint): boolean => {
    return windowWidth >= breakpoints[breakpoint];
  };

  // Current breakpoint calculation
  const getCurrentBreakpoint = (): Breakpoint => {
    if (windowWidth < breakpoints.sm) return 'sm';
    if (windowWidth < breakpoints.md) return 'md';
    if (windowWidth < breakpoints.lg) return 'lg';
    if (windowWidth < breakpoints.xl) return 'xl';
    return '2xl';
  };

  return {
    windowWidth,
    getIsMobile,
    getIsDesktop,
    isMobile: getIsMobile(),
    isDesktop: getIsDesktop(),
    isBreakpointBetween,
    matchesBreakpoint,
    currentBreakpoint: getCurrentBreakpoint(),
    breakpoints,
  };
};
