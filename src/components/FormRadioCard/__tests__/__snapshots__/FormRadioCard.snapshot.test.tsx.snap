// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FormRadioCard Snapshots > matches No option radio card snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1"
  >
    <label
      class="_radio-field_31042b _normal_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          class="_radio_31042b"
          name="yesno"
          type="radio"
          value="no"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            No
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches Yes option radio card snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1"
  >
    <label
      class="_radio-field_31042b _normal_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          class="_radio_31042b"
          name="yesno"
          type="radio"
          value="yes"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            Yes
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches all states radio cards snapshot 1`] = `
<div>
  <div
    class="space-y-4"
  >
    <div
      class="flex gap-3"
    >
      <div
        class="_card_e7a3d1"
      >
        <label
          class="_radio-field_31042b _normal_31042b"
        >
          <div
            class="_content_31042b"
          >
            <input
              class="_radio_31042b"
              name="normal"
              type="radio"
              value="unchecked"
            />
            <div
              class="_label-helper_31042b"
            >
              <div
                class="_label_31042b"
              >
                Normal Unchecked
              </div>
            </div>
          </div>
        </label>
      </div>
      <div
        class="_card_e7a3d1 _selected_e7a3d1"
      >
        <label
          class="_radio-field_31042b _normal_31042b"
        >
          <div
            class="_content_31042b"
          >
            <input
              checked=""
              class="_radio_31042b _checked_31042b"
              name="normal"
              type="radio"
              value="checked"
            />
            <div
              class="_label-helper_31042b"
            >
              <div
                class="_label_31042b"
              >
                Normal Checked
              </div>
            </div>
          </div>
        </label>
      </div>
    </div>
    <div
      class="flex gap-3"
    >
      <div
        class="_card_e7a3d1 _disabled_e7a3d1"
      >
        <label
          class="_radio-field_31042b _disabled_31042b"
        >
          <div
            class="_content_31042b"
          >
            <input
              class="_radio_31042b _disabled_31042b"
              disabled=""
              name="disabled"
              type="radio"
              value="disabled-unchecked"
            />
            <div
              class="_label-helper_31042b"
            >
              <div
                class="_label_31042b"
              >
                Disabled Unchecked
              </div>
            </div>
          </div>
        </label>
      </div>
      <div
        class="_card_e7a3d1 _selected_e7a3d1 _disabled_e7a3d1"
      >
        <label
          class="_radio-field_31042b _disabled_31042b"
        >
          <div
            class="_content_31042b"
          >
            <input
              checked=""
              class="_radio_31042b _checked_31042b _disabled_31042b"
              disabled=""
              name="disabled"
              type="radio"
              value="disabled-checked"
            />
            <div
              class="_label-helper_31042b"
            >
              <div
                class="_label_31042b"
              >
                Disabled Checked
              </div>
            </div>
          </div>
        </label>
      </div>
    </div>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches checked radio card snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1 _selected_e7a3d1"
  >
    <label
      class="_radio-field_31042b _normal_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          checked=""
          class="_radio_31042b _checked_31042b"
          name="test"
          type="radio"
          value="test-value"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            Test Option
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches default radio card snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1"
  >
    <label
      class="_radio-field_31042b _normal_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          class="_radio_31042b"
          name="test"
          type="radio"
          value="test-value"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            Test Option
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches disabled checked radio card snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1 _selected_e7a3d1 _disabled_e7a3d1"
  >
    <label
      class="_radio-field_31042b _disabled_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          checked=""
          class="_radio_31042b _checked_31042b _disabled_31042b"
          disabled=""
          name="test"
          type="radio"
          value="test-value"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            Test Option
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches disabled radio card snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1 _disabled_e7a3d1"
  >
    <label
      class="_radio-field_31042b _disabled_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          class="_radio_31042b _disabled_31042b"
          disabled=""
          name="test"
          type="radio"
          value="test-value"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            Test Option
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches long label radio card snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1"
  >
    <label
      class="_radio-field_31042b _normal_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          class="_radio_31042b"
          name="test"
          type="radio"
          value="test-value"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            This is a very long label that might wrap to multiple lines
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches property feature radio cards snapshot 1`] = `
<div>
  <div
    class="flex gap-3"
  >
    <div
      class="_card_e7a3d1 _selected_e7a3d1"
    >
      <label
        class="_radio-field_31042b _normal_31042b"
      >
        <div
          class="_content_31042b"
        >
          <input
            checked=""
            class="_radio_31042b _checked_31042b"
            name="garden"
            type="radio"
            value="yes"
          />
          <div
            class="_label-helper_31042b"
          >
            <div
              class="_label_31042b"
            >
              Yes
            </div>
          </div>
        </div>
      </label>
    </div>
    <div
      class="_card_e7a3d1"
    >
      <label
        class="_radio-field_31042b _normal_31042b"
      >
        <div
          class="_content_31042b"
        >
          <input
            class="_radio_31042b"
            name="garden"
            type="radio"
            value="no"
          />
          <div
            class="_label-helper_31042b"
          >
            <div
              class="_label_31042b"
            >
              No
            </div>
          </div>
        </div>
      </label>
    </div>
  </div>
</div>
`;

exports[`FormRadioCard Snapshots > matches radio card with custom className snapshot 1`] = `
<div>
  <div
    class="_card_e7a3d1 custom-radio-card"
  >
    <label
      class="_radio-field_31042b _normal_31042b"
    >
      <div
        class="_content_31042b"
      >
        <input
          class="_radio_31042b"
          name="test"
          type="radio"
          value="test-value"
        />
        <div
          class="_label-helper_31042b"
        >
          <div
            class="_label_31042b"
          >
            Test Option
          </div>
        </div>
      </div>
    </label>
  </div>
</div>
`;
