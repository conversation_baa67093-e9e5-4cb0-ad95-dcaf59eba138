import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { FormRadioCard } from '../FormRadioCard';

export default describe('FormRadioCard Snapshots', () => {
  const defaultProps = {
    label: 'Test Option',
    checked: false,
    onChange: vi.fn(),
    name: 'test',
    value: 'test-value',
  };

  it('matches default radio card snapshot', () => {
    const { container } = render(<FormRadioCard {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches checked radio card snapshot', () => {
    const { container } = render(<FormRadioCard {...defaultProps} checked={true} />);
    expect(container).toMatchSnapshot();
  });

  it('matches disabled radio card snapshot', () => {
    const { container } = render(<FormRadioCard {...defaultProps} disabled />);
    expect(container).toMatchSnapshot();
  });

  it('matches disabled checked radio card snapshot', () => {
    const { container } = render(<FormRadioCard {...defaultProps} checked={true} disabled />);
    expect(container).toMatchSnapshot();
  });

  it('matches Yes option radio card snapshot', () => {
    const { container } = render(
      <FormRadioCard {...defaultProps} label="Yes" name="yesno" value="yes" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches No option radio card snapshot', () => {
    const { container } = render(
      <FormRadioCard {...defaultProps} label="No" name="yesno" value="no" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches radio card with custom className snapshot', () => {
    const { container } = render(<FormRadioCard {...defaultProps} className="custom-radio-card" />);
    expect(container).toMatchSnapshot();
  });

  it('matches long label radio card snapshot', () => {
    const { container } = render(
      <FormRadioCard
        {...defaultProps}
        label="This is a very long label that might wrap to multiple lines"
      />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches property feature radio cards snapshot', () => {
    const { container } = render(
      <div className="flex gap-3">
        <FormRadioCard label="Yes" checked={true} onChange={vi.fn()} name="garden" value="yes" />
        <FormRadioCard label="No" checked={false} onChange={vi.fn()} name="garden" value="no" />
      </div>
    );
    expect(container).toMatchSnapshot();
  });

  it('matches all states radio cards snapshot', () => {
    const { container } = render(
      <div className="space-y-4">
        <div className="flex gap-3">
          <FormRadioCard
            label="Normal Unchecked"
            checked={false}
            onChange={vi.fn()}
            name="normal"
            value="unchecked"
          />
          <FormRadioCard
            label="Normal Checked"
            checked={true}
            onChange={vi.fn()}
            name="normal"
            value="checked"
          />
        </div>
        <div className="flex gap-3">
          <FormRadioCard
            label="Disabled Unchecked"
            checked={false}
            onChange={vi.fn()}
            name="disabled"
            value="disabled-unchecked"
            disabled
          />
          <FormRadioCard
            label="Disabled Checked"
            checked={true}
            onChange={vi.fn()}
            name="disabled"
            value="disabled-checked"
            disabled
          />
        </div>
      </div>
    );
    expect(container).toMatchSnapshot();
  });
});
