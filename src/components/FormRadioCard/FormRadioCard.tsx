import React from 'react';
import classNames from 'classnames';
import { Radio } from '../Radio';
import { RadioState } from '../Radio/Radio.types';
import { FormRadioCardProps } from './FormRadioCard.types';
import styles from './FormRadioCard.module.scss';

export const FormRadioCard: React.FC<FormRadioCardProps> = ({
  label,
  checked,
  onChange,
  name,
  value,
  disabled = false,
  className,
}) => {
  return (
    <div
      className={classNames(
        styles.card,
        {
          [styles.selected]: checked,
          [styles.disabled]: disabled,
        },
        className
      )}
    >
      <Radio
        labelText={label}
        checked={checked}
        onChange={onChange}
        state={disabled ? RadioState.DISABLED : RadioState.NORMAL}
        name={name}
        value={value}
      />
    </div>
  );
};
