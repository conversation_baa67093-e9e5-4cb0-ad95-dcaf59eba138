.card {
  border: 1px solid var(--colors-gray-200);
  border-radius: 8px;
  padding: 16px;
  height: 48px;
  cursor: pointer;
  transition: border-color 0.2s ease;
  flex: 1;
  display: flex;
  align-items: center;

  &:hover {
    border-color: var(--colors-gray-300);
  }

  &.selected {
    border: 1px solid var(--colors-green-600);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  :global([class*='content']) {
    align-items: center;
  }
}

@media (max-width: 768px) {
  .card {
    flex: none;
    width: calc(50% - 4px);
    padding: 12px;
  }
}
