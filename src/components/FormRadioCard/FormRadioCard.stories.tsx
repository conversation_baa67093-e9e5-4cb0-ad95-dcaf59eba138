import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { FormRadioCard } from './FormRadioCard';

const meta: Meta<typeof FormRadioCard> = {
  title: 'Components/FormRadioCard',
  component: FormRadioCard,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: `
## FormRadioCard

A radio button component styled as a card for better visual hierarchy in forms.

### Features
- Card-style radio button with custom styling
- Consistent with design system colors
- Disabled state support
- Responsive design
- White background for radio buttons as per design requirements

### Usage

\`\`\`jsx
import { FormRadioCard } from '@/components/FormRadioCard';

const [selected, setSelected] = useState('');

<FormRadioCard
  label="Yes"
  checked={selected === 'yes'}
  onChange={() => setSelected('yes')}
  name="question"
  value="yes"
/>
\`\`\`
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Label text for the radio button',
    },
    checked: {
      control: 'boolean',
      description: 'Whether the radio button is checked',
    },
    onChange: {
      action: 'changed',
      description: 'Callback fired when radio button is clicked',
    },
    name: {
      control: 'text',
      description: 'Name attribute for the radio button group',
    },
    value: {
      control: 'text',
      description: 'Value of the radio button',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the radio button is disabled',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: '200px', padding: '20px' }}>
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof FormRadioCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Option',
    checked: false,
    name: 'default',
    value: 'option',
    disabled: false,
  },
};

export const Checked: Story = {
  args: {
    ...Default.args,
    checked: true,
  },
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
  },
};

export const DisabledChecked: Story = {
  args: {
    ...Default.args,
    checked: true,
    disabled: true,
  },
};

export const YesNoOptions: Story = {
  render: () => {
    const [selected, setSelected] = useState('');

    return (
      <div className="flex gap-3">
        <FormRadioCard
          label="Yes"
          checked={selected === 'yes'}
          onChange={() => setSelected('yes')}
          name="yesno"
          value="yes"
        />
        <FormRadioCard
          label="No"
          checked={selected === 'no'}
          onChange={() => setSelected('no')}
          name="yesno"
          value="no"
        />
      </div>
    );
  },
};

export const PropertyFeatures: Story = {
  render: () => {
    const [features, setFeatures] = useState({
      garden: '',
      balcony: '',
      pool: '',
    });

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium mb-2">Garden</h3>
          <div className="flex gap-3">
            <FormRadioCard
              label="Yes"
              checked={features.garden === 'yes'}
              onChange={() => setFeatures((prev) => ({ ...prev, garden: 'yes' }))}
              name="garden"
              value="yes"
            />
            <FormRadioCard
              label="No"
              checked={features.garden === 'no'}
              onChange={() => setFeatures((prev) => ({ ...prev, garden: 'no' }))}
              name="garden"
              value="no"
            />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Balcony/Terrace</h3>
          <div className="flex gap-3">
            <FormRadioCard
              label="Yes"
              checked={features.balcony === 'yes'}
              onChange={() => setFeatures((prev) => ({ ...prev, balcony: 'yes' }))}
              name="balcony"
              value="yes"
            />
            <FormRadioCard
              label="No"
              checked={features.balcony === 'no'}
              onChange={() => setFeatures((prev) => ({ ...prev, balcony: 'no' }))}
              name="balcony"
              value="no"
            />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Swimming Pool</h3>
          <div className="flex gap-3">
            <FormRadioCard
              label="Yes"
              checked={features.pool === 'yes'}
              onChange={() => setFeatures((prev) => ({ ...prev, pool: 'yes' }))}
              name="pool"
              value="yes"
            />
            <FormRadioCard
              label="No"
              checked={features.pool === 'no'}
              onChange={() => setFeatures((prev) => ({ ...prev, pool: 'no' }))}
              name="pool"
              value="no"
            />
          </div>
        </div>
      </div>
    );
  },
};

export const AllStates: Story = {
  render: () => {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium mb-2">Normal States</h3>
          <div className="flex gap-3">
            <FormRadioCard
              label="Unchecked"
              checked={false}
              onChange={() => {}}
              name="normal"
              value="unchecked"
            />
            <FormRadioCard
              label="Checked"
              checked={true}
              onChange={() => {}}
              name="normal"
              value="checked"
            />
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium mb-2">Disabled States</h3>
          <div className="flex gap-3">
            <FormRadioCard
              label="Disabled"
              checked={false}
              onChange={() => {}}
              name="disabled"
              value="disabled"
              disabled
            />
            <FormRadioCard
              label="Disabled Checked"
              checked={true}
              onChange={() => {}}
              name="disabled"
              value="disabled-checked"
              disabled
            />
          </div>
        </div>
      </div>
    );
  },
};
