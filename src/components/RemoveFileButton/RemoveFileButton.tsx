import React from 'react';
import { CloseIcon } from '@/components/CloseIcon/CloseIcon';
import styles from './RemoveFileButton.module.scss';

interface RemoveFileButtonProps {
  onRemove?: () => void;
  className?: string;
}

export const RemoveFileButton: React.FC<RemoveFileButtonProps> = ({
  onRemove,
  className,
}) => {
  if (!onRemove) return null;

  return (
    <button
      className={`${styles.removeButton} ${className || ''}`}
      onClick={onRemove}
      aria-label="Remove file"
    >
      <CloseIcon size={8} strokeWidth={1.2} />
    </button>
  );
};
