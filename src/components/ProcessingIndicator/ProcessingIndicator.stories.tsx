import type { Meta, StoryObj } from '@storybook/react';
import { ProcessingIndicator } from './ProcessingIndicator';

const meta: Meta<typeof ProcessingIndicator> = {
  title: 'Components/ProcessingIndicator',
  component: ProcessingIndicator,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof ProcessingIndicator>;

export const Default: Story = {
  args: {},
};
