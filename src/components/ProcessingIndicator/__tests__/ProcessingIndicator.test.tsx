import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { vi } from 'vitest';
import { ProcessingIndicator } from '../ProcessingIndicator';
import styles from '../ProcessingIndicator.module.scss';

vi.mock('@hugeicons/react', () => ({
  IconSvgObject: {},
}));

vi.mock('@hugeicons-pro/core-stroke-standard', () => ({
  Search01Icon: { name: 'Search01Icon' },
  BubbleChatIcon: { name: 'BubbleChatIcon' },
  Loading01Icon: { name: 'Loading01Icon' },
}));

vi.mock('@/components/HugeiconsIcon', () => ({
  HugeiconsIcon: ({ className }: { className?: string }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Icon
    </div>
  ),
}));

// Mock the TypewriterText component to render text immediately
vi.mock('../ProcessingIndicator', async () => {
  const actual = await vi.importActual('../ProcessingIndicator');

  return {
    ...actual,
    ProcessingIndicator: ({ className = '' }: { className?: string }) => {
      const steps = [
        {
          id: 'uploading',
          label: 'Uploading',
          icon: { name: 'Search01Icon' },
        },
        {
          id: 'reading',
          label: 'Reading the document',
          icon: { name: 'BubbleChatIcon' },
        },
        {
          id: 'personalizing',
          label: 'Personalising Alfie for you',
          icon: { name: 'Loading01Icon' },
        },
      ];

      return (
        <div className={`${className}`} data-testid="processing-indicator">
          <div className={styles.stepsContainer}>
            {steps.map((step, index) => (
              <div key={step.id} className={styles.step}>
                <div className={styles.stepIndicator}>
                  <div className={styles.stepIcon}>
                    <div data-testid="mock-icon">Mock Icon</div>
                  </div>
                  {index < steps.length - 1 && <div className={styles.stepConnector} />}
                </div>
                <div className={styles.stepContent}>
                  <div className={styles.stepLabel}>
                    <span className={styles.typewriterText}>
                      {step.label}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    },
  };
});

describe('ProcessingIndicator', () => {
  it('renders with default steps', () => {
    const { container } = render(<ProcessingIndicator />);

    // Check for the step labels structure
    const stepLabels = screen.getAllByTestId('mock-icon');
    expect(stepLabels).toHaveLength(3);

    // Check that the text appears
    expect(container.textContent).toContain('Uploading');
  });

  it('applies custom className', () => {
    const customClass = 'custom-processing-indicator';
    const { container } = render(<ProcessingIndicator className={customClass} />);

    expect(container.firstChild).toHaveClass(customClass);
  });

  it('renders icons for all steps', () => {
    render(<ProcessingIndicator />);

    const icons = screen.getAllByTestId('mock-icon');
    expect(icons).toHaveLength(3);
  });

  it('renders step connectors between steps', () => {
    const { container } = render(<ProcessingIndicator />);

    // Use CSS modules class name
    const connectors = container.querySelectorAll(`.${styles.stepConnector}`);
    expect(connectors).toHaveLength(2);
  });

  it('has typewriter animation for step labels', () => {
    const { container } = render(<ProcessingIndicator />);

    // Check that step labels exist
    const stepLabels = container.querySelectorAll(`.${styles.stepLabel}`);
    expect(stepLabels).toHaveLength(3);

    // Check that typewriter text elements exist
    const typewriterTexts = container.querySelectorAll(`.${styles.typewriterText}`);
    expect(typewriterTexts).toHaveLength(3);
  });

  it('renders all three predefined steps', () => {
    const { container } = render(<ProcessingIndicator />);

    // Check for all step texts in the DOM
    expect(container.textContent).toContain('Uploading');
    expect(container.textContent).toContain('Reading the document');
    expect(container.textContent).toContain('Personalising Alfie for you');
  });

  it('has proper step structure', () => {
    const { container } = render(<ProcessingIndicator />);

    const stepElements = container.querySelectorAll(`.${styles.step}`);
    expect(stepElements).toHaveLength(3);

    stepElements.forEach((step) => {
      expect(step.querySelector(`.${styles.stepIndicator}`)).toBeInTheDocument();
      expect(step.querySelector(`.${styles.stepIcon}`)).toBeInTheDocument();
      expect(step.querySelector(`.${styles.stepContent}`)).toBeInTheDocument();
      expect(step.querySelector(`.${styles.stepLabel}`)).toBeInTheDocument();
    });
  });
});
