.container {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid var(--colors-gray-200);
  max-width: 100%;
  width: 100%;
}

.stepsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
}

.stepIndicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.stepIcon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: #4b5563;
  font-size: 11px;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.stepConnector {
  width: 1px;
  height: 20px;
  background: #e5e7eb;
  margin-top: 4px;
}

.stepContent {
  flex: 1;
}

.stepLabel {
  font-size: 16px;
  font-weight: bold;
  color: var(--colors-black);
  min-height: 24px;
}

.typewriterText {
  display: inline;
}

.cursor {
  display: inline-block;
  background-color: var(--colors-black);
  width: 2px;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }

  51%,
  100% {
    opacity: 0;
  }
}
