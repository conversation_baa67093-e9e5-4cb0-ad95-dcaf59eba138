import React, { useState, useEffect } from 'react';
import { ProcessingIndicatorProps } from './ProcessingIndicator.types';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { Search01Icon, BubbleChatIcon, Loading01Icon } from '@hugeicons-pro/core-stroke-standard';
import styles from './ProcessingIndicator.module.scss';

interface TypewriterTextProps {
  text: string;
  delay?: number;
  speed?: number;
}

const TypewriterText: React.FC<TypewriterTextProps> = ({ text, delay = 0, speed = 50 }) => {
  const [displayedText, setDisplayedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isStarted, setIsStarted] = useState(false);

  useEffect(() => {
    const startTimer = setTimeout(() => {
      setIsStarted(true);
    }, delay);

    return () => clearTimeout(startTimer);
  }, [delay]);

  useEffect(() => {
    if (!isStarted) return;

    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayedText(text.slice(0, currentIndex + 1));
        setCurrentIndex(currentIndex + 1);
      }, speed);

      return () => clearTimeout(timer);
    }
  }, [currentIndex, text, speed, isStarted]);

  return (
    <span className={styles.typewriterText}>
      {displayedText}
      {currentIndex < text.length && <span className={styles.cursor}>|</span>}
    </span>
  );
};

export const ProcessingIndicator: React.FC<ProcessingIndicatorProps> = ({ className = '' }) => {
  const steps = [
    {
      id: 'uploading',
      label: 'Uploading',
      icon: Search01Icon,
    },
    {
      id: 'reading',
      label: 'Reading the document',
      icon: BubbleChatIcon,
    },
    {
      id: 'personalizing',
      label: 'Personalising Alfie for you',
      icon: Loading01Icon,
    },
  ];

  return (
    <div className={`${styles.container} ${className}`}>
      <div className={styles.stepsContainer}>
        {steps.map((step, index) => (
          <div key={step.id} className={styles.step}>
            <div className={styles.stepIndicator}>
              <div className={styles.stepIcon}>
                <HugeiconsIcon icon={step.icon as unknown as IconSvgObject} size={13} />
              </div>
              {index < steps.length - 1 && <div className={styles.stepConnector} />}
            </div>
            <div className={styles.stepContent}>
              <div className={styles.stepLabel}>
                <TypewriterText text={step.label} delay={index * 1000} speed={30} />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
