import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import clsx from 'clsx';
import { useUser } from '@clerk/nextjs';
import { Modal } from '../Modal';
import { PersonalizationCard } from '../PersonalizationCard';
import { PersonalizationCardField } from '../PersonalizationCard/PersonalizationCard.types';
import { UserInfoModalProps } from './UserInfoModal.types';
import { useWidgets } from '@/hooks/useWidgets';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { ConfirmDialog } from '../ConfirmDialog';
import { AddressField, AddressFieldRef } from './components';
import { UserInfoFieldBase, UserInfoFieldBaseRef } from './components/UserInfoFieldBase';
import styles from './UserInfoModal.module.scss';
import useJobSubmissionValidation from '@/hooks/useJobSubmissionValidation';
import useClerkDetailsHandler from '@/hooks/useClerkDetailsHandler';
import useBackendClient from '@/hooks/useBackendClient';

export const UserInfoModal: React.FC<UserInfoModalProps> = ({
  open,
  onClose,
  readOnly = false,
  inline = false,
  renderToBody = true,
}) => {
  const { user, isLoaded } = useUser();
  const { client } = useBackendClient();
  const { addressValue, fetchProperties } = useWidgets();
  const [mounted, setMounted] = useState(false);
  const { setPhone, setEmailAddress, setFirstName, setLastName, error } = useClerkDetailsHandler();

  const [isEditing, setIsEditing] = useState(false);
  const [editingFields, setEditingFields] = useState<Set<string>>(new Set());
  const [showCloseConfirmDialog, setShowCloseConfirmDialog] = useState(false);

  const firstNameRef = useRef<UserInfoFieldBaseRef>(null);
  const lastNameRef = useRef<UserInfoFieldBaseRef>(null);
  const emailRef = useRef<UserInfoFieldBaseRef>(null);
  const phoneRef = useRef<UserInfoFieldBaseRef>(null);
  const addressRef = useRef<AddressFieldRef>(null);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  const handleEditStart = useCallback((fieldId: string) => {
    setEditingFields((prev) => new Set(prev).add(fieldId));
  }, []);

  const handleEditEnd = useCallback((fieldId: string) => {
    setEditingFields((prev) => {
      const newSet = new Set(prev);
      newSet.delete(fieldId);
      return newSet;
    });
  }, []);

  const { areRequiredFieldsFilled } = useJobSubmissionValidation();

  const handleModalClose = useCallback(() => {
    if (!areRequiredFieldsFilled) {
      setShowCloseConfirmDialog(true);
      return;
    }

    setEditingFields(new Set());
    setIsEditing(false);
    onClose();
  }, [onClose, areRequiredFieldsFilled]);

  const handleConfirmClose = useCallback(() => {
    setShowCloseConfirmDialog(false);
    setEditingFields(new Set());
    setIsEditing(false);
    onClose();
  }, [onClose]);

  const handleCancelClose = useCallback(() => {
    setShowCloseConfirmDialog(false);
  }, []);

  useEffect(() => {
    if (open) {
      fetchProperties(client);
    }
  }, [open, fetchProperties, client]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleSave = () => {
    setIsEditing(false);
  };

  const handleContinueToChat = () => {
    onClose();
  };

  const currentEmailAddress = user?.emailAddresses?.[0]?.emailAddress;
  const currentPhoneNumber = user?.phoneNumbers?.[0]?.phoneNumber;

  const fields: PersonalizationCardField[] = useMemo(
    () => [
      {
        id: 'firstName',
        label: 'First name',
        value: user?.firstName || '',
        editable: false,
        type: 'component',
        component: (
          <UserInfoFieldBase
            ref={firstNameRef}
            initialValue={user?.firstName || ''}
            onEditStart={() => handleEditStart('firstName')}
            onEditEnd={() => handleEditEnd('firstName')}
            onSave={setFirstName}
            placeholderText="First name"
            buttonLabel="Save first name"
            error={error?.field === 'firstName' ? error.errorMessage : undefined}
          />
        ),
        showEditButton: !editingFields.has('firstName'),
        onEditClick: () => firstNameRef.current?.startEditing(),
      },
      {
        id: 'lastName',
        label: 'Last name',
        value: user?.lastName || '',
        editable: false,
        type: 'component',
        component: (
          <UserInfoFieldBase
            ref={lastNameRef}
            initialValue={user?.lastName || ''}
            onEditStart={() => handleEditStart('lastName')}
            onEditEnd={() => handleEditEnd('lastName')}
            onSave={setLastName}
            placeholderText="Last name"
            buttonLabel="Save last name"
            error={error?.field === 'lastName' ? error.errorMessage : undefined}
          />
        ),
        showEditButton: !editingFields.has('lastName'),
        onEditClick: () => lastNameRef.current?.startEditing(),
      },
      {
        id: 'email',
        label: 'Email address',
        value: currentEmailAddress || '',
        editable: false,
        type: 'component',
        component: (
          <UserInfoFieldBase
            ref={emailRef}
            initialValue={currentEmailAddress || ''}
            onEditStart={() => handleEditStart('email')}
            onEditEnd={() => handleEditEnd('email')}
            onSave={setEmailAddress}
            placeholderText="Email address"
            buttonLabel="Save email"
            error={error?.field === 'email' ? error.errorMessage : undefined}
          />
        ),
        showEditButton: !editingFields.has('email'),
        onEditClick: () => {
          emailRef.current?.startEditing();
        },
      },
      {
        id: 'phone',
        label: 'Phone number',
        value: currentPhoneNumber || '',
        editable: false,
        type: 'component',
        component: (
          <UserInfoFieldBase
            ref={phoneRef}
            initialValue={currentPhoneNumber || ''}
            onEditStart={() => handleEditStart('phone')}
            onEditEnd={() => handleEditEnd('phone')}
            onSave={setPhone}
            placeholderText="Phone number"
            buttonLabel="Save phone number"
            error={error?.field === 'phoneNumber' ? error.errorMessage : undefined}
          />
        ),
        showEditButton: !editingFields.has('phone'),
        onEditClick: () => phoneRef.current?.startEditing(),
      },
      {
        id: 'address',
        label: 'Address',
        value: addressValue || '',
        editable: false,
        type: 'component',
        component: (
          <AddressField
            ref={addressRef}
            initialValue={addressValue || ''}
            onEditStart={() => handleEditStart('address')}
            onEditEnd={() => handleEditEnd('address')}
          />
        ),
        showEditButton: !editingFields.has('address') && !addressValue,
        onEditClick: () => addressRef.current?.startEditing(),
      },
    ],
    [
      addressValue,
      currentEmailAddress,
      currentPhoneNumber,
      editingFields,
      error?.errorMessage,
      error?.field,
      handleEditEnd,
      handleEditStart,
      setEmailAddress,
      setFirstName,
      setLastName,
      setPhone,
      user?.firstName,
      user?.lastName,
    ]
  );

  if (!isLoaded || !mounted) {
    if (readOnly && inline) {
      return <div>Loading...</div>;
    }
    return (
      <Modal open={open} onClose={handleModalClose} title="Your details">
        <div>Loading...</div>
      </Modal>
    );
  }

  if (readOnly && inline) {
    const inlineContent = (
      <div className={clsx('UserInfoModal', styles.modalWrapper)}>
        <div className={styles.PersonalizationCard}>
          <PersonalizationCard
            title="Your details"
            titleEditable={false}
            fields={fields}
            files={[]}
            isEditing={false}
            onEdit={() => {}}
            onCancel={() => {}}
            onSave={() => {}}
            inline={true}
            readOnly={true}
            alignLabelsTop={true}
          />
        </div>
      </div>
    );

    return renderToBody ? createPortal(inlineContent, document.body) : inlineContent;
  }

  const modalContent = (
    <Modal open={open} onClose={handleModalClose} title="Your details">
      <div className={clsx('UserInfoModal', styles.modalWrapper)}>
        <div className={styles.PersonalizationCard}>
          <PersonalizationCard
            title=""
            titleEditable={false}
            fields={fields}
            files={[]}
            isEditing={isEditing}
            onEdit={handleEdit}
            onCancel={handleCancel}
            onSave={handleSave}
            inline={true}
            alignLabelsTop={true}
          />
        </div>
        <div className={styles.continueButton}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            state={areRequiredFieldsFilled ? ButtonState.DEFAULT : ButtonState.DISABLED}
            onClick={handleContinueToChat}
          >
            Continue to chat
          </Button>
        </div>
        <ConfirmDialog
          title="Required details missing"
          description="We need a verified email, verified phone number, and your address to proceed with this booking"
          isOpen={showCloseConfirmDialog}
          onCancel={handleConfirmClose}
          onOk={handleCancelClose}
          cancelText="Cancel anyway"
          okText="Continue"
          cancelType="danger"
        />
      </div>
    </Modal>
  );

  return renderToBody ? createPortal(modalContent, document.body) : modalContent;
};
