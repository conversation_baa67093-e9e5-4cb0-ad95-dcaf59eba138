import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { Button } from '../../Button';
import { AddressInput } from '@/components/AddressInput/AddressInput';
import { AddressType } from '@/components/AddressInput/AddressInput.types';
import { ButtonSize, ButtonState, ButtonType } from '../../Button/Button.types';
import { useWidgets } from '@/hooks/useWidgets';
import styles from './FieldComponents.module.scss';
import useBackendClient from '@/hooks/useBackendClient';

interface AddressFieldProps {
  onChange?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
  editable?: boolean;
  initialValue: string;
}

export interface AddressFieldRef {
  startEditing: () => void;
}

export const AddressField = forwardRef<AddressFieldRef, AddressFieldProps>(
  ({ initialValue, onEditStart, onEditEnd, editable }, ref) => {
    const { client } = useBackendClient();
    const { saveAddress: saveAddressToStore } = useWidgets();
    const [isEditing, setIsEditing] = useState(false);
    const [value, setValue] = useState<AddressType>();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const startEditing = useCallback(() => {
      setValue(undefined);
      setIsEditing(true);
      setError('');
      onEditStart?.();
    }, [onEditStart]);

    useEffect(() => {
      if (editable) {
        startEditing();
      }
    }, [editable, startEditing]);

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        startEditing();
      },
    }));

    const handleSave = useCallback(async () => {
      if (!value) {
        return;
      }
      setLoading(true);
      setError('');

      try {
        await saveAddressToStore(value, client);
        setIsEditing(false);
        onEditEnd?.();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to save address';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    }, [client, onEditEnd, saveAddressToStore, value]);
    const isDisabled = loading || !value;

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <AddressInput
            onChange={setValue}
            loading={loading}
            hideTitle={true}
            hideBorder={true}
            placeholder="Find address by postcode"
            manualEntryText="Enter address manually"
            containerClassName={styles.noPaddingContainer}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div className={styles.actions + (!error ? ' ' + styles.buttonWithMargin : '')}>
            <Button
              type={ButtonType.PRIMARY}
              size={ButtonSize.BASE}
              state={isDisabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
            >
              Save address
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.displayContainer}>
        <span className={styles.value}>{initialValue || '—'}</span>
      </div>
    );
  }
);

AddressField.displayName = 'AddressField';
