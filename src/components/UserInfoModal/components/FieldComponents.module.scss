.editingContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.error {
  font-size: 14px;
  color: var(--colors-red-500);
}

.displayContainer {
  display: flex;
  align-items: center;
  width: 100%;
}

.value {
  font-size: 16px;
  color: var(--colors-black);
}

.input {
  width: 100%;

  :global(.input) {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--colors-gray-200);
    border-radius: 8px;
    font-family: 'Quasimoda', sans-serif;
    font-size: 14px;
    color: var(--colors-black);
    background: var(--colors-white);
    height: 40px;

    &::placeholder {
      color: var(--colors-gray-400);
      opacity: 1;
    }

    &:focus {
      outline: none;
      border-color: var(--colors-green-primary);
    }
  }
}

.actions {
  display: flex;
  gap: 8px;
}

.buttonWithMargin {
  margin-top: 8px;
}

.noPaddingContainer {
  padding: 0 !important;
  min-height: inherit !important;
}
