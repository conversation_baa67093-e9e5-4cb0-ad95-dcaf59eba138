import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { Button } from '../../Button';
import { Input } from '../../Input';
import { ButtonSize, ButtonType } from '../../Button/Button.types';
import { InputState } from '../../Input/Input.types';
import styles from './FieldComponents.module.scss';

export interface UserInfoFieldBaseProps {
  initialValue: string;
  onSave?: (value: string) => Promise<void>;
  onEditStart?: () => void;
  onEditEnd?: () => void;
  placeholderText: string;
  buttonLabel: string;
  error?: string;
}

export interface UserInfoFieldBaseRef {
  startEditing: () => void;
}

export const UserInfoFieldBase = forwardRef<UserInfoFieldBaseRef, UserInfoFieldBaseProps>(
  ({ error, initialValue, onSave, onEditStart, onEditEnd, placeholderText, buttonLabel }, ref) => {
    const [isEditing, setIsEditing] = useState(false);
    const [value, setValue] = useState(initialValue);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      if (!isEditing) {
        setValue(initialValue);
      }
    }, [initialValue, isEditing]);

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(initialValue);
        setIsEditing(true);
        onEditStart?.();
      },
    }));

    const handleSave = useCallback(async () => {
      setLoading(true);

      try {
        await onSave?.(value);
        setIsEditing(false);
        onEditEnd?.();
      } catch (e) {
        console.error('Failed to save user info:', e);
      } finally {
        setLoading(false);
      }
    }, [onEditEnd, onSave, value]);

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <Input
            value={value}
            onChange={setValue}
            placeholderText={placeholderText}
            showLabel={false}
            showHelperText={false}
            showLeftIcon={false}
            state={InputState.NORMAL}
            disableClear={true}
            className={styles.input}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div className={styles.actions + (!error ? ' ' + styles.buttonWithMargin : '')}>
            <Button
              type={ButtonType.PRIMARY}
              size={ButtonSize.BASE}
              onClick={handleSave}
              disabled={loading}
            >
              {buttonLabel}
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.displayContainer}>
        <span className={styles.value}>{value || '—'}</span>
      </div>
    );
  }
);

UserInfoFieldBase.displayName = 'UserInfoFieldBase';
