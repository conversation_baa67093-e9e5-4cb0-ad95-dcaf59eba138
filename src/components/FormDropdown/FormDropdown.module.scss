.container {
  position: relative;
  width: 100%;
}

.trigger {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--colors-gray-200);
  border-radius: 8px;
  font-size: 16px;
  color: var(--colors-gray-900);
  outline: none;
  transition: border-color 0.2s;
  min-height: 48px;
  font-family: 'Quasimoda', Helvetica;
  cursor: pointer;
  background: var(--colors-white);
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    border-color: var(--colors-gray-300);
  }

  &:focus {
    border-color: var(--colors-green-600);
  }

  &.active {
    border-color: var(--colors-green-600);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.arrow {
  color: var(--colors-gray-500);
  pointer-events: none;
  transition: transform 0.2s ease;

  &.rotated {
    transform: rotate(180deg);
  }
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--colors-white);
  border-radius: 0 0 8px 8px;
  border: 1px solid var(--colors-green-600);
  border-top: none;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  z-index: 10;
}

.option {
  padding: 12px;
  font-size: 16px;
  color: var(--colors-gray-900);
  cursor: pointer;
  transition: background-color 0.2s;
  font-family: 'Quasimoda', Helvetica;
  border-bottom: 1px solid var(--colors-gray-100);

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: var(--colors-green-50);
  }
}

@media (max-width: 768px) {
  .trigger {
    min-height: 44px;
  }
}
