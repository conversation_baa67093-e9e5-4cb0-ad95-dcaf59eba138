import React from 'react';
import classNames from 'classnames';
import styles from './NotificationTag.module.scss';
import { useRouter } from 'next/navigation';

export interface NotificationTagProps {
  children: React.ReactNode;
  className?: string;
}

export const NotificationTag: React.FC<NotificationTagProps> = ({ children, className }) => {
  const router = useRouter();
  return (
    <div
      className={classNames(styles.notificationTag, className, styles.flex)}
      onClick={() => router.push('/property-profile')}
    >
      {children}
    </div>
  );
};

export default NotificationTag;
