import React from 'react';
import { NotificationTag } from './NotificationTag';
import type { Meta, StoryObj } from '@storybook/react';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { ArrowRight01Icon } from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';

const meta: Meta<typeof NotificationTag> = {
  title: 'Components/NotificationTag',
  component: NotificationTag,
  tags: ['autodocs'],
};

export default meta;

export const Default: StoryObj<typeof NotificationTag> = {
  render: () => (
    <NotificationTag>
      Added to Property Profile
      <HugeiconsIcon
        icon={ArrowRight01Icon as unknown as IconSvgObject}
        size={12}
        color="#145543"
        strokeWidth={0.5}
      />
    </NotificationTag>
  ),
};
