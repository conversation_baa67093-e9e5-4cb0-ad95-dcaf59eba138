import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { NotificationTag } from '../NotificationTag';

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  }),
}));

const defaultProps = {
  children: 'Test notification',
  className: undefined,
};

describe('NotificationTag Snapshots', () => {
  it('matches basic notification tag snapshot', () => {
    const { container } = render(<NotificationTag {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches notification tag with custom className snapshot', () => {
    const { container } = render(<NotificationTag {...defaultProps} className="custom-class" />);
    expect(container).toMatchSnapshot();
  });

  it('matches notification tag with complex children snapshot', () => {
    const { container } = render(
      <NotificationTag>
        <span>Added to Property Profile</span>
        <div>Icon</div>
      </NotificationTag>
    );
    expect(container).toMatchSnapshot();
  });

  it('matches empty notification tag snapshot', () => {
    const { container } = render(<NotificationTag>{''}</NotificationTag>);
    expect(container).toMatchSnapshot();
  });
});
