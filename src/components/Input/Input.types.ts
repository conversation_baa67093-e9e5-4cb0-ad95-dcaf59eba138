// import * as HugeIcons from '@hugeicons/react';
import { HTMLInputAutoCompleteAttribute, ReactNode } from 'react';

export enum InputSize {
  LARGE = 'large',
  REGULAR = 'regular',
  SMALL = 'small',
}

export enum InputState {
  NORMAL = 'normal',
  SUCCESS = 'success',
  ERROR = 'error',
  DISABLED = 'disabled',
}

export interface InputProps {
  value?: string;
  onChange?: (value: string) => void;
  onClear?: () => void;
  labelText?: string;
  placeholderText?: string;
  helperText?: ReactNode | string;
  size?: InputSize;
  state?: InputState;
  className?: string;
  inputClassName?: string;
  showLabel?: boolean;
  showHelperText?: boolean;
  showLeftIcon?: boolean;
  showPlaceholder?: boolean;
  leftIconName?: string;
  fullWidth?: boolean;
  width?: number;
  disableClear?: boolean;
  greenBorder?: boolean;
  inputType?: 'text' | 'number' | 'email' | 'password' | 'tel' | 'url';
  autoComplete?: HTMLInputAutoCompleteAttribute;
}
