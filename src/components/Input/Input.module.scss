.input-field {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  position: relative;
  font-family: 'Quasimoda', Helvetica;

  &.fullWidth {
    width: 100%;
  }

  .label {
    align-self: stretch;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    margin-top: -1px;
    position: relative;
  }

  .input {
    align-items: center;
    align-self: stretch;
    border: 1px solid var(--colors-gray-200);
    border-radius: 8px;
    display: flex;
    flex: 0 0 auto;
    gap: var(--spacing-2);
    position: relative;
    width: 100%;
    height: 48px;

    .content {
      align-items: center;
      display: flex;
      flex: 1;
      gap: var(--spacing-2);
      padding: 0 8px;
    }

    .input-text {
      background: transparent;
      border: none;
      flex: 1;
      font-family: 'Quasimoda', sans-serif;
      outline: none;
      width: 100%;
      font-size: 16px;
      color: #222;
      padding: 10px 0;

      &::placeholder {
        color: #b3b3b3;
        opacity: 1;
      }
    }

    .user-instance {
      height: 16px;
      width: 16px;
      position: relative;
      color: var(--colors-gray-500);
    }

    .instance-node {
      height: 16px;
      width: 16px;
      position: relative;
      color: var(--colors-gray-500);
    }

    &:hover:not(.disabled) {
      border-color: var(--colors-gray-300);
    }

    &:focus-within:not(.disabled) {
      border-color: #007a6e;
      background-color: var(--colors-white);
    }

    &.normal {
      background-color: var(--colors-white);
      border-color: var(--colors-gray-200);
    }

    &.disabled {
      background-color: var(--colors-gray-50);
      border-color: var(--colors-gray-300);
      cursor: not-allowed;

      .input-text {
        cursor: not-allowed;
        color: var(--colors-gray-400);
      }
    }

    &.error {
      background-color: var(--colors-red-50);
      border-color: var(--colors-red-500);
    }

    &.success {
      background-color: var(--colors-green-50);
      border-color: var(--colors-green-500);
    }
  }

  &.disabled .label {
    color: var(--colors-gray-400);
  }

  .large {
    height: 52px;

    .input-text {
      font-size: 16px;
    }
  }

  .regular {
    height: 48px;

    .input-text {
      font-size: 16px;
    }
  }

  .small {
    height: 37px;

    .input-text {
      font-size: 14px;
    }
  }

  .caption {
    color: var(--colors-gray-500);
    font-size: 14px;
    line-height: 21px;

    a {
      color: var(--colors-gray-900);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }

      &.error {
        color: var(--colors-red-600);
      }

      &.success {
        color: var(--colors-green-600);
      }
    }
  }

  &.disabled .caption {
    color: var(--colors-gray-400);

    a {
      color: var(--colors-gray-400);
      pointer-events: none;
    }
  }

  .clear-button {
    position: absolute;
    right: 12px;
  }

  &.greenBorder {
    .input {
      border: 1px solid var(--colors-gray-200);
      transition: border-color 0.2s;

      &:hover {
        border-color: var(--colors-gray-300);
      }

      &:focus,
      &:focus-visible,
      &:focus-within {
        border-color: #007a6e !important;
        background-color: var(--colors-white) !important;
      }

      .input-text {
        min-height: 48px;
      }

      @media (max-width: 768px) {
        .input-text {
          min-height: 44px;
        }
      }
    }
  }
}