'use client';

import { useTheme } from 'next-themes';
import { Toaster as Sonner, ToasterProps, toast as sonnerToast } from 'sonner';
import { Toast, ToastColor } from '../Toast';

interface ToastProps {
  title: string;
}

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="!w-[90vw] !max-w-md !left-1/2 !-translate-x-1/2 lg:!-translate-x-0 lg:!w-[766px] lg:!max-w-[766px] lg:!left-[calc(50%+130px)]"
      toastOptions={{
        style: {
          width: '100%',
        },
      }}
      {...props}
      duration={2000}
    />
  );
};

export const toast = {
  success: (toast: Omit<ToastProps, 'id'>) => {
    return sonnerToast.custom(() => <Toast color={ToastColor.SUCCESS} headingText={toast.title} />);
  },
  info: (toast: Omit<ToastProps, 'id'>) => {
    return sonnerToast.custom(() => <Toast color={ToastColor.INFO} headingText={toast.title} />);
  },
};

export { Toaster };
