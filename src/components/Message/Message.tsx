import React, { memo, useEffect } from 'react';
import { MessageTypeValue } from '@/types/messages';
import { MessageProps } from './Message.types';
import { TextMessage } from './components/TextMessage';
// import { ImageMessage } from './components/ImageMessage';

export const Message: React.FC<MessageProps> = memo(
  ({
    message,
    className,
    messages,
    children,
    showNotificationTag,
  }) => {
    switch (message?.type) {
      case MessageTypeValue.Image:
        //   return <ImageMessage message={message} className={className} />;
        return null;
      case MessageTypeValue.Text:
      default:
        return (
          <TextMessage
            message={message}
            className={className}
            messages={messages}
            showNotificationTag={showNotificationTag}
          >
            {children}
          </TextMessage>
        );
    }
  }
);

Message.displayName = 'Message';
