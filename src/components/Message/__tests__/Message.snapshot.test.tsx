import React from 'react';
import { render } from '@testing-library/react';
import { Message } from '../Message';
import { MessageTypeValue } from '@/types/messages';
import { vi } from 'vitest';

const mockTextMessage = {
  id: 1,
  content: 'Hello world',
  type: MessageTypeValue.Text,
  senderType: 'user' as const,
  timestamp: '2024-01-01T12:00:00.000Z',
};

const mockImageMessage = {
  id: 2,
  content: 'Image caption',
  type: MessageTypeValue.Image,
  senderType: 'system' as const,
  timestamp: '2024-01-01T12:00:00.000Z',
  additionalData: {
    imageUrl: 'https://example.com/image.jpg',
    category: 'test',
  },
};

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('next/headers.js', () => ({
  headers: () => new Map(),
  cookies: () => new Map(),
}));

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
  }),
}));

vi.mock('../../ClickableImages', () => ({
  ClickableImages: () => null,
}));

describe('Message Snapshots', () => {
  it('matches user text message snapshot', () => {
    const { container } = render(<Message message={mockTextMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches system text message snapshot', () => {
    const systemMessage = { ...mockTextMessage, senderType: 'system' as const };
    const { container } = render(<Message message={systemMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches system image message snapshot', () => {
    const { container } = render(<Message message={mockImageMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches message with custom className snapshot', () => {
    const { container } = render(
      <Message message={mockTextMessage} className="custom-class" />
    );
    expect(container).toMatchSnapshot();
  });

  it('matches empty content message snapshot', () => {
    const emptyMessage = { ...mockTextMessage, content: '' };
    const { container } = render(<Message message={emptyMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches long content message snapshot', () => {
    const longMessage = { ...mockTextMessage, content: 'a'.repeat(1000) };
    const { container } = render(<Message message={longMessage} />);
    expect(container).toMatchSnapshot();
  });

  it('matches message with special characters snapshot', () => {
    const specialCharsMessage = {
      ...mockTextMessage,
      content: '!@#$%^&*()_+<>?:"{}|',
    };
    const { container } = render(<Message message={specialCharsMessage} />);
    expect(container).toMatchSnapshot();
  });
});
