.container {
  padding-top: 8px;
  width: 100%;

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(2, minmax(194px, 1fr));
  gap: 12px;
  width: 100%;
  max-width: 400px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, minmax(136px, 1fr));
    gap: 8px;
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, minmax(106px, 1fr));
  }
}

.imageItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  height: 257px;
  gap: 8px;
  border: 1px solid #3e9681;
  border-radius: 8px;
  cursor: pointer;
  box-shadow: 0px 3.34px 6.69px -3.34px #0000000d,
    0px 6.69px 10.03px -1.67px #0000001a;
  text-align: center;

  &:hover .image {
    transform: scale(1.03);
  }

  @media (max-width: 768px) {
    height: 209px;
    padding: 8px;
  }

  @media (max-width: 480px) {
    height: 179px;
  }
}

.imageWrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 170px;
  overflow: hidden;
  border-radius: 8px;

  @media (max-width: 768px) {
    padding-bottom: 120px;
  }

  @media (max-width: 480px) {
    padding-bottom: 90px;
  }
}

.image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease-in-out;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
