import React, { useEffect, useState } from 'react';
import styles from './ClickableImages.module.scss';
import { Message } from '@/types/messages';
import classNames from 'classnames';
import { filterValidImages } from '@/utils/messageUtils';
import useMessageSender from '@/hooks/useMessageSender';

interface ClickableImagesProps {
  message?: Message;
  isDisabled?: boolean;
  onImagesLoaded?: () => void;
}

export const ClickableImages: React.FC<ClickableImagesProps> = ({
  message,
  isDisabled = false,
  onImagesLoaded,
}) => {
  const { sendMessage } = useMessageSender();
  const [validImageUrls, setValidImageUrls] = useState<
    Array<{ imageUrl: string; description: string }>
  >([]);

  useEffect(() => {
    const loadValidImages = async () => {
      if (
        !message?.additionalData?.imageClickableUrls ||
        message.additionalData?.imageClickableUrls.length === 0
      ) {
        onImagesLoaded?.();
        return;
      }

      const imageUrls = message.additionalData.imageClickableUrls.map((item) => item.imageUrl);
      const validUrls = await filterValidImages(imageUrls);

      const validImages = message.additionalData.imageClickableUrls.filter((item) =>
        validUrls.includes(item.imageUrl)
      );

      setValidImageUrls(validImages);
      onImagesLoaded?.();
    };

    loadValidImages();
  }, [message?.additionalData?.imageClickableUrls, onImagesLoaded]);

  if (
    !message?.additionalData?.imageClickableUrls ||
    message.additionalData?.imageClickableUrls.length === 0 ||
    validImageUrls.length === 0
  ) {
    return null;
  }

  return (
    <div
      className={classNames(styles.container, {
        [styles.disabled]: isDisabled,
      })}
    >
      <div className={styles.imageGrid}>
        {validImageUrls.map(({ imageUrl, description }, index) => (
          <div key={`${imageUrl}-${index}`} className={styles.imageItem}>
            <div className={styles.imageWrapper}>
              <img
                src={imageUrl}
                alt={`Image ${index + 1}`}
                className={styles.image}
                onClick={() => !isDisabled && sendMessage(description)}
              />
            </div>
            <div className={styles.imageDescription}>{description}</div>
          </div>
        ))}
      </div>
    </div>
  );
};
