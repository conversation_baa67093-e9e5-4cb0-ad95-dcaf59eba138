.container {
  padding-top: 8px;
  width: 100%;

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 8px;
  width: 100%;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

.imageItemContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.imageItem {
  display: flex;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  max-width: 320px;
  height: 186px;
}

.caption {
  text-align: center;
  color: #145543;
  font-size: 14px;
}

.imageWrapper {
  position: relative;
  width: 100%;
}

.image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover;
  pointer-events: none;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
