import React from 'react';

export const LogoHA: React.FC = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 140 140"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="70" cy="70" r="70" fill="#BB7B1C" />
    <mask
      id="mask0_836_6772"
      style={{ maskType: 'alpha' }}
      maskUnits="userSpaceOnUse"
      x="62"
      y="54"
      width="69"
      height="36"
    >
      <rect x="62.2148" y="54" width="67.8761" height="36" fill="#D9D9D9" />
    </mask>
    <g mask="url(#mask0_836_6772)"></g>
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M67.1674 55.3636C69.0983 54.4582 71.6837 54.0109 74.9347 54.0109V54C77.4983 54 79.6256 54.1091 81.3056 54.3055L79.691 64.4727C78.5892 64.4073 77.4765 64.3636 76.3528 64.3636C75.2947 64.3636 74.5419 64.5491 74.0946 64.8982C73.6474 65.2473 73.4292 65.7818 73.4292 66.4909C73.4292 67.4727 74.0183 68.1382 75.1965 68.5091C76.3746 68.88 78.4365 68.8691 81.3928 68.4873V78.0982C76.2001 77.1273 73.6037 77.7818 73.6037 80.0509C73.6037 81.6764 73.6365 82.9527 73.7128 83.88C73.7892 84.8073 73.9637 86.0727 74.2255 87.6655H61.2983C61.5056 82.44 61.6037 75.72 61.6037 67.5164C61.6037 64.2109 62.0401 61.5927 62.9347 59.64C63.8292 57.6873 65.2365 56.2691 67.1674 55.3636ZM28.571 80.8582C28.3637 80.4545 28.0474 80.1818 27.6219 80.04C27.1965 79.8873 26.5528 79.8218 25.691 79.8218C24.6001 79.8218 23.8583 80.0182 23.4765 80.4218C23.0946 80.8255 22.8437 81.6 22.7237 82.7455L22.1892 87.6545H10.0146C10.1346 86.4764 10.3419 84.4691 10.6365 81.6327C11.1056 77.2364 11.6619 72.8182 12.2946 68.3782C12.9274 63.9382 13.6474 59.4436 14.4437 54.8945H38.171C38.9674 59.4436 39.6874 63.9382 40.3201 68.3782C40.9528 72.8182 41.5092 77.2364 41.9783 81.6327C42.2728 84.4691 42.4801 86.4764 42.6001 87.6545H29.5419L29.0074 82.7455C28.9201 81.8945 28.7674 81.2618 28.5601 80.8691L28.571 80.8582ZM27.9056 71.28C28.2547 70.6909 28.331 69.5564 28.1237 67.8764C27.9165 65.8364 27.6219 64.5164 27.2401 63.8945C26.8583 63.2727 26.4001 62.9673 25.8656 62.9673C25.331 62.9673 24.8728 63.2836 24.491 63.9164C24.1092 64.5491 23.8146 65.8691 23.6074 67.8764C23.4328 69.5564 23.4874 70.6909 23.7819 71.28C24.0765 71.8691 24.7092 72.1636 25.691 72.1636C26.8147 72.1636 27.5456 71.8691 27.9056 71.28ZM57.7533 87.6546H45.4042C45.4915 86.0946 45.5679 83.8364 45.6224 80.9018C45.6769 77.9673 45.7097 74.6509 45.7097 70.9636C45.7097 64.1127 45.6006 58.7564 45.4042 54.8946H57.4042L57.4479 70.9636C57.4479 74.6509 57.4806 77.9673 57.5351 80.9018C57.5897 83.8364 57.666 86.0946 57.7533 87.6546ZM97.7677 87.6546H85.8659C85.9531 86.4109 86.0295 84.6328 86.084 82.32C86.1386 80.0073 86.1713 77.3782 86.1713 74.4655C86.1713 68.7382 86.0622 64.3528 85.8659 61.32H97.4622V74.4655C97.4622 77.3891 97.4949 80.0073 97.5495 82.32C97.604 84.6328 97.6804 86.4109 97.7677 87.6546ZM117.829 79.0036C119.662 79.0036 121.331 78.8073 122.836 78.4036L122.815 78.4145C124.266 78.0327 125.651 77.4764 126.993 76.7564L125.346 87.1091C124.713 87.2945 123.982 87.48 123.153 87.6436C121.222 88.0255 118.844 88.2218 116.007 88.2218C112.255 88.2218 109.266 87.7636 107.018 86.8473C104.782 85.9309 103.146 84.48 102.131 82.5055C101.116 80.52 100.604 77.9018 100.604 74.5309C100.604 71.16 101.073 68.4873 102.022 66.4909C102.971 64.4945 104.476 63.0436 106.538 62.1273C108.6 61.2109 111.382 60.7527 114.862 60.7527C119.433 60.7527 122.716 61.4945 124.713 62.9673C126.709 64.4509 127.702 66.4036 127.702 68.8582C127.702 71.9564 126.644 73.9745 124.538 74.9018C122.422 75.8291 120.218 76.2982 117.916 76.2982C116.913 76.2982 115.876 76.2436 114.818 76.1236C113.967 76.0364 113.4 76.0255 113.138 76.1018C112.866 76.1782 112.735 76.4182 112.735 76.8327C112.735 77.4764 113.215 78 114.175 78.4036C115.135 78.8073 116.356 79.0036 117.829 79.0036ZM115.56 66.9491C114.567 66.9491 113.749 67.4073 113.116 68.3236L113.127 68.3127C112.484 69.2291 112.244 70.5818 112.407 72.3709C114.229 72.6109 115.713 72.5018 116.847 72.0436C117.982 71.5855 118.549 70.7564 118.549 69.5673C118.549 68.8145 118.266 68.1927 117.709 67.6909C117.153 67.2 116.433 66.9491 115.56 66.9491Z"
      fill="#342B25"
    />
  </svg>
);
