import React from 'react';
import classNames from 'classnames';
import { ImageMessageProps } from './ImageMessage.types';
import styles from './ImageMessage.module.scss';
import { LogoHA } from '../LogoHA';
import Image from 'next/image';

export const ImageMessage: React.FC<ImageMessageProps> = ({
  message,
  className,
}) => {
  const isUserMessage = message.senderType === 'user';
  const isSystemMessage = message.senderType === 'system';
  const imageUrl = message.additionalData?.imageUrl;

  if (!imageUrl) return null;

  return (
    <div className={styles.messageWrapper}>
      <div
        className={classNames(styles.messageContainer, className, {
          [styles.userContainer]: isUserMessage,
          [styles.systemContainer]: isSystemMessage,
        })}
      >
        {isSystemMessage && (
          <div className={styles.logoContainer} data-testid="logo-container">
            <LogoHA />
          </div>
        )}
        <div
          className={classNames(styles.bubble, {
            [styles.userBubble]: isUserMessage,
            [styles.systemBubble]: isSystemMessage,
          })}
        >
          <Image
            data-testid="message-image"
            src={imageUrl}
            alt={message.content || 'Image'}
            width={500}
            height={300}
            className={styles.image}
          />
          {message.content && (
            <p className={styles.caption}>{message.content}</p>
          )}
        </div>
      </div>
    </div>
  );
};
