import React, { useEffect, useMemo, useRef } from 'react';
import classNames from 'classnames';
import { TextMessageProps } from './TextMessage.types';
import { LogoHA } from '../LogoHA';
import { extractButtons, mapDocumentsToFetchedFile, mapFetchedFilesToDocuments, stripButtonsFromText } from '@/utils/messageUtils';
import { QuickActions } from '@/components/QuickActions';
import { MarkdownRenderer } from '@/components/MarkdownRenderer/MarkdownRenderer';
import { ClickableImages } from '../ClickableImages';
import { NonClickableImages } from '../NonClickableImages';
import { ConfirmToast } from '@/components/ConfirmToast';
import { NotificationTag } from '@/components/NotificationTag';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { ArrowRight01Icon } from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';

import styles from './TextMessage.module.scss';
import { MessageDocumentPreview } from '@/components/FileUploadManager/components/MessageDocumentPreview';
import { UniversalFile } from '@/types/file';
import { FileUploadPreview } from '@/components/Composer/components/FileUploadPreview/FileUploadPreview';

export const TextMessage: React.FC<TextMessageProps> = ({
  message,
  className,
  isAIThinking = false,
  messages,
  children,
  showNotificationTag,
}) => {
  const isUserMessage = message?.senderType === 'user';
  const isSystemMessage = message?.senderType === 'system' || isAIThinking;
  const wrapperRef = useRef<HTMLDivElement>(null);
  const files = mapDocumentsToFetchedFile(message?.documents);
  const imageFiles = useMemo(() => files.filter((file) => file.type === 'image'), [files]);
  const nonImageFiles = useMemo(() => files.filter((file) => file.type !== 'image'), [files]);

  const buttons = message?.content ? extractButtons(message.content) : [];
  const cleanText = message?.content ? stripButtonsFromText(message.content) : '';

  const isDisabled = message?.senderType === 'system' && message?.id !== (messages || [])[0]?.id;

  const isLastMessage = message?.id === (messages || [])[0]?.id;
  const jobSummary = message?.additionalData?.jobSummary;

  return (
    <div className={styles.messageWrapper} data-testid="message-wrapper">
      <div
        ref={wrapperRef}
        className={classNames(styles.messageContainer, className, {
          [styles.userContainer]: isUserMessage,
          [styles.systemContainer]: isSystemMessage,
        })}
      >
        {isSystemMessage && (
          <div className={styles.logoContainer}>
            <LogoHA />
          </div>
        )}
        <div className={styles.attachmentsContainer}>
          {nonImageFiles.length > 0 && (
            <div className={styles.previewsContainer}>
              <div className={classNames(styles.itemsContainer, styles.nonImageUploads)}>
                {nonImageFiles?.map((fileUpload, index) => (
                  <MessageDocumentPreview key={index} file={fileUpload} index={index} />
                ))}
              </div>
            </div>
          )}
        </div>
        <div className={styles.messageRow}>
          <div
            className={classNames(styles.bubble, {
              [styles.userBubble]: isUserMessage,
              [styles.systemBubble]: isSystemMessage,
            })}
          >
            {isAIThinking ? (
              <div className={styles.loadingDots} />
            ) : (
              <div className={styles.messageContent}>
                {showNotificationTag && (
                  <NotificationTag>
                    Added to Property Profile
                    <HugeiconsIcon
                      icon={ArrowRight01Icon as unknown as IconSvgObject}
                      size={12}
                      color="#145543"
                      strokeWidth={0.5}
                    />
                  </NotificationTag>
                )}
                {imageFiles.length > 0 && (
                  <div className={styles.previewsContainer}>
                    <div className={styles.itemsContainer}>
                      {imageFiles.map((file: UniversalFile, index: number) => (
                        <FileUploadPreview key={index} file={file} />
                      ))}
                    </div>
                  </div>
                )}
                <div className={styles.content}>
                  <MarkdownRenderer content={cleanText} />
                </div>
                {children}
                <NonClickableImages message={message} />
                <ClickableImages message={message} isDisabled={isDisabled} />
                {buttons.length > 0 && !jobSummary && (
                  <div className={!isDisabled ? styles.buttonsContainer : styles.disabled}>
                    <QuickActions buttons={buttons} />
                  </div>
                )}
                {isLastMessage && jobSummary && (
                  <ConfirmToast
                    isJobSummary={!!jobSummary}
                    isLastMessage={isLastMessage}
                    message={message}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
