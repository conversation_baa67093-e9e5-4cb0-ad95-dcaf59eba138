import React from 'react';
import { render, screen } from '@testing-library/react';
import { TextMessage } from '../TextMessage';
import { MessageTypeValue } from '@/types/messages';
import { vi } from 'vitest';

const mockMessage = {
  id: 1,
  content: 'Hello world',
  type: MessageTypeValue.Text,
  senderType: 'user' as const,
  timestamp: new Date().toISOString(),
};

vi.mock('../../ClickableImages', () => ({
  ClickableImages: () => null,
}));

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('next/headers.js', () => ({
  headers: () => new Map(),
  cookies: () => new Map(),
}));

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
  }),
}));

describe('TextMessage Component', () => {
  describe('Rendering', () => {
    it('renders text message correctly', () => {
      render(<TextMessage message={mockMessage} />);
      expect(screen.getByText('Hello world')).toBeInTheDocument();
    });

    it.skip('applies custom className', () => {
      const { container } = render(<TextMessage message={mockMessage} className="custom-class" />);
      const messageContainer = container.querySelector('._messageContainer_73128e');
      expect(messageContainer).toBeTruthy();
      expect(messageContainer).toHaveClass('custom-class');
    });

    it.skip('renders system message with logo', () => {
      const systemMessage = { ...mockMessage, senderType: 'system' as const };
      const { container } = render(<TextMessage message={systemMessage} />);
      const logoContainer = container.querySelector('._logoContainer_73128e');
      expect(logoContainer).toBeTruthy();
    });

    it('renders user message without logo', () => {
      const { container } = render(<TextMessage message={mockMessage} />);
      const logoContainer = container.querySelector('._logoContainer_73128e');
      expect(logoContainer).toBeFalsy();
    });
  });

  describe('Message Styling', () => {
    it.skip('applies correct container styles for user messages', () => {
      const { container } = render(<TextMessage message={mockMessage} />);
      const messageContainer = container.querySelector('._messageContainer_73128e');
      expect(messageContainer).toBeTruthy();
      expect(messageContainer).toHaveClass('_userContainer_73128e');
    });

    it.skip('applies correct container styles for system messages', () => {
      const systemMessage = { ...mockMessage, senderType: 'system' as const };
      const { container } = render(<TextMessage message={systemMessage} />);
      const messageContainer = container.querySelector('._messageContainer_73128e');
      expect(messageContainer).toBeTruthy();
      expect(messageContainer).toHaveClass('_systemContainer_73128e');
    });

    it.skip('applies correct bubble styles for user messages', () => {
      const { container } = render(<TextMessage message={mockMessage} />);
      const bubble = container.querySelector('._bubble_73128e');
      expect(bubble).toBeTruthy();
      expect(bubble).toHaveClass('_userBubble_73128e');
    });

    it.skip('applies correct bubble styles for system messages', () => {
      const systemMessage = { ...mockMessage, senderType: 'system' as const };
      const { container } = render(<TextMessage message={systemMessage} />);
      const bubble = container.querySelector('._bubble_73128e');
      expect(bubble).toBeTruthy();
      expect(bubble).toHaveClass('_systemBubble_73128e');
    });
  });

  describe('Content Handling', () => {
    it.skip('handles empty content', () => {
      const emptyMessage = { ...mockMessage, content: '' };
      const { container } = render(<TextMessage message={emptyMessage} />);
      const content = container.querySelector('._content_73128e');
      expect(content).toBeTruthy();
      expect(content?.textContent).toBe('');
    });

    it('handles long content', () => {
      const longMessage = { ...mockMessage, content: 'a'.repeat(1000) };
      render(<TextMessage message={longMessage} />);
      expect(screen.getByText('a'.repeat(1000))).toBeInTheDocument();
    });

    it('handles special characters', () => {
      const specialCharsMessage = {
        ...mockMessage,
        content: '!@#$%^&*()_+<>?:"{}|',
      };
      render(<TextMessage message={specialCharsMessage} />);
      expect(screen.getByText('!@#$%^&*()_+<>?:"{}|')).toBeInTheDocument();
    });
  });
});
