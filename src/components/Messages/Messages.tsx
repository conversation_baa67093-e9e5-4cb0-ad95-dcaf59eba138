import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Message } from '@/components/Message';
import styles from './Messages.module.scss';
import { MessagesProps } from './Messages.types';
import { Message as MessageType, MessageTypeValue } from '@/types/messages';

import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { fetchJob } from '@/api/jobs';
import { getCurrentUser } from '@/api/user';
import { Alert } from '../Alert/Alert';
import { AlertColor } from '../Alert/Alert.types';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { useChats } from '@/hooks/useChats';
import useMessageSender from '@/hooks/useMessageSender';
import { UserInfoModal } from '../UserInfoModal';
import { InfoBox } from '../InfoBox';
import { HugeiconsIcon } from '../HugeiconsIcon';
import {
  ArrowReloadHorizontalIcon,
  BubbleChatIcon,
  Mic02Icon,
} from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';
import useJobSubmissionValidation from '@/hooks/useJobSubmissionValidation';
import { GuestUserInfoModal } from '@/components/GuestUserInfoModal';
import useBackendClient from '@/hooks/useBackendClient';
import { mapFetchedFilesToDocuments } from '@/utils/messageUtils';
import { TextMessage } from '@/components/Message/components/TextMessage';

// Memoized component for handling notification tags per message
const NotificationTagManager: React.FC<{
  messageId: number;
  messageNotificationTags: Record<number, boolean>;
  messages: MessageType[];
  children: (showNotificationTag: boolean) => React.ReactNode;
}> = memo(({ messageId, messageNotificationTags, messages, children }) => {
  const showNotificationTag = useMemo(() => {
    // Find the message and its index
    const messageIndex = messages.findIndex((msg) => msg.id === messageId);
    const message = messages[messageIndex];

    if (!message || message.senderType !== 'system' || messageIndex >= messages.length - 1) {
      return false;
    }

    // Check the previous user message (next in array = previous chronologically)
    const prevMsg = messages[messageIndex + 1];
    if (!prevMsg || prevMsg.senderType !== 'user') {
      return false;
    }

    // Check both the updated message documents and the notification tag state
    const hasExistingRelevantDocs =
      Array.isArray(prevMsg.documents) &&
      prevMsg.documents.some((doc) =>
        ['appliance', 'files', 'property details'].includes(doc.category?.toLowerCase?.() || '')
      );

    const hasNotificationTag = messageNotificationTags[prevMsg.id] || false;

    return hasExistingRelevantDocs || hasNotificationTag;
  }, [messageId, messageNotificationTags, messages]);

  return <>{children(showNotificationTag)}</>;
});

NotificationTagManager.displayName = 'NotificationTagManager';

export const Messages: React.FC<MessagesProps> = ({ hasMore, chatId, onLoadMore }): JSX.Element => {
  const router = useRouter();
  const sentinelRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isSignedIn } = useAuth();
  const [isGuestFlow, setIsGuestFlow] = useState(!isSignedIn);
  const { isRetryButtonShown, isStreamingMessage, optimisticMessage, chats } = useChats();
  const { sendMessage } = useMessageSender();
  const { client } = useBackendClient();
  const messages = useMemo(
    () => chats.find((c) => c.id === chatId)?.messages ?? [],
    [chatId, chats]
  );
  const lastMessage = messages[0];

  const [messageNotificationTags, setMessageNotificationTags] = useState<Record<number, boolean>>(
    {}
  );
  const [lockedNotificationTags, setLockedNotificationTags] = useState<Record<number, boolean>>({});
  const [isUserInfoModalOpen, setIsUserInfoModalOpen] = useState(false);

  const [showItLooksGoodMessage, setShowItLooksGoodMessage] = useState(false);
  const [userDetailsValidated, setUserDetailsValidated] = useState(false);

  const {
    jobSummaryConfirmed,
    setJobSummaryConfirmed,
    showItLooksGoodMessage: persistentShowItLooksGoodMessage,
    setShowItLooksGoodMessage: setPersistentShowItLooksGoodMessage,
    userDetailsValidated: persistentUserDetailsValidated,
    setUserDetailsValidated: setPersistentUserDetailsValidated,
  } = useMessages();

  const [isValidationErrorShown, setIsValidationErrorShown] = useState(false);
  const [isValidatingDetails, setIsValidatingDetails] = useState(false);

  useEffect(() => {
    if (persistentShowItLooksGoodMessage !== showItLooksGoodMessage) {
      setShowItLooksGoodMessage(persistentShowItLooksGoodMessage);
    }
    if (persistentUserDetailsValidated !== userDetailsValidated) {
      setUserDetailsValidated(persistentUserDetailsValidated);
    }
  }, [
    persistentShowItLooksGoodMessage,
    persistentUserDetailsValidated,
    showItLooksGoodMessage,
    userDetailsValidated,
  ]);

  useEffect(() => {
    if (showItLooksGoodMessage !== persistentShowItLooksGoodMessage) {
      setPersistentShowItLooksGoodMessage(showItLooksGoodMessage);
    }
  }, [
    showItLooksGoodMessage,
    persistentShowItLooksGoodMessage,
    setPersistentShowItLooksGoodMessage,
  ]);

  useEffect(() => {
    if (userDetailsValidated !== persistentUserDetailsValidated) {
      setPersistentUserDetailsValidated(userDetailsValidated);
    }
  }, [userDetailsValidated, persistentUserDetailsValidated, setPersistentUserDetailsValidated]);

  const activeNotificationTags = isStreamingMessage
    ? lockedNotificationTags
    : messageNotificationTags;

  useEffect(() => {
    if (!isStreamingMessage) {
      setMessageNotificationTags({});
      setLockedNotificationTags({});
      setIsValidationErrorShown(false);

      if (!jobSummaryConfirmed) {
        setUserDetailsValidated(false);
        setShowItLooksGoodMessage(false);
        setPersistentUserDetailsValidated(false);
        setPersistentShowItLooksGoodMessage(false);
      }
    }
  }, [
    chatId,
    isStreamingMessage,
    jobSummaryConfirmed,
    setPersistentShowItLooksGoodMessage,
    setPersistentUserDetailsValidated,
  ]);

  const { fetchProperties } = useWidgets();

  const { areRequiredFieldsFilled } = useJobSubmissionValidation();

  useEffect(() => {
    const sentinel = sentinelRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && hasMore) {
            onLoadMore?.();
          }
        });
      },
      {
        root: containerRef.current,
        threshold: 0.1,
      }
    );

    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
    };
  }, [hasMore, onLoadMore]);

  useEffect(() => {
    fetchProperties(client);
  }, [client, fetchProperties]);

  const checkJobSummaryStatus = useCallback(async () => {
    const jobId = messages.map((m) => m.additionalData?.jobSummary?.jobId).find((jobId) => !!jobId);

    if (jobId) {
      if (!isSignedIn) {
        setJobSummaryConfirmed(false);
        return;
      }

      try {
        const response = await fetchJob({
          jobId: Number(jobId),
          client,
        });
        const jobStatus = response.status;
        const isConfirmed = jobStatus === 'user_accepted';
        setJobSummaryConfirmed(isConfirmed);

        if (isConfirmed) {
          setUserDetailsValidated(true);
          setShowItLooksGoodMessage(true);
          setPersistentUserDetailsValidated(true);
          setPersistentShowItLooksGoodMessage(true);
        }
      } catch (error) {
        setJobSummaryConfirmed(false);
      }
    } else {
      setJobSummaryConfirmed(false);
    }
  }, [
    client,
    isSignedIn,
    messages,
    setJobSummaryConfirmed,
    setPersistentShowItLooksGoodMessage,
    setPersistentUserDetailsValidated,
  ]);

  useEffect(() => {
    checkJobSummaryStatus();
  }, [checkJobSummaryStatus]);

  const handleClickRetry = async () => {
    const content = optimisticMessage?.content;
    if (content) {
      await sendMessage(content, optimisticMessage?.attachments);
    }
  };

  const handleDetailsLookGood = async () => {
    setIsValidatingDetails(true);
    setIsValidationErrorShown(false);

    const maxRetries = 3;
    const retryDelay = 1000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      if (attempt > 1) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      }

      try {
        const userDetails = await getCurrentUser({ client });

        if (!!userDetails.canUserAcceptJobs) {
          setUserDetailsValidated(true);
          setShowItLooksGoodMessage(true);
          setPersistentUserDetailsValidated(true);
          setPersistentShowItLooksGoodMessage(true);
          setIsValidatingDetails(false);
          return;
        }
      } catch (retryError) {
        console.error(`Retry attempt ${attempt} failed:`, retryError);
      }
    }

    setIsValidationErrorShown(true);
    setIsValidatingDetails(false);
  };

  const handleStartNewChat = useCallback(() => {
    router.push('/');
  }, [router]);

  const handleNavigateToNewChatWithMessage = useCallback(
    (message: string) => {
      const encodedMessage = encodeURIComponent(message);
      router.push(`/chats?query=${encodedMessage}`);
    },
    [router]
  );

  const isAiThinking =
    isStreamingMessage && (optimisticMessage || !lastMessage || lastMessage?.senderType === 'user');
  return (
    <div ref={containerRef} className={styles.messagesContainer}>
      <div className={styles.messagesWrapper}>
        {isRetryButtonShown && (
          <Alert
            inline
            color={AlertColor.WARNING}
            headingText="It looks like the service is temporarily unavailable, please try again in a few minutes"
            button={
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.ORANGE}
                size={ButtonSize.XS}
                onClick={handleClickRetry}
              >
                Retry
              </Button>
            }
          />
        )}
        {isAiThinking && <TextMessage isAIThinking />}
        {optimisticMessage && (
          <Message
            key={'__OptimisticMessage__'}
            isAIThinking={isStreamingMessage}
            message={{
              id: -1,
              content: optimisticMessage.content,
              documents: mapFetchedFilesToDocuments(optimisticMessage.attachments),
              type: MessageTypeValue.Text,
              timestamp: new Date().toISOString(),
              senderType: 'user',
              isOptimistic: true,
            }}
          />
        )}
        {messages.map((message, index) => {
          const jobSummary = message.additionalData?.jobSummary;
          const isLastMessage = index === 0;
          const shouldDisableButtons = jobSummaryConfirmed || !isLastMessage;
          const shouldDisableItLooksGood =
            shouldDisableButtons ||
            !areRequiredFieldsFilled ||
            showItLooksGoodMessage ||
            isValidatingDetails;

          if (jobSummary) {
            return (
              <div key={index}>
                {!areRequiredFieldsFilled ? (
                  <Message
                    key="contact-details-missing-message"
                    message={{
                      id: -998, // Stable negative ID to avoid conflicts with real messages
                      content: `Got it. I can now find you the right quotes from trusted local professionals.

Just confirm a few quick details. It’s fast, secure, and commitment-free.
What to expect:
 • Multiple quotes, one point of contact
 • Your details stay private
 • £1,000 guarantee if you do book`,
                      documents: [],
                      type: MessageTypeValue.Text,
                      timestamp: '2024-01-01T00:00:00.000Z', // Stable timestamp
                      senderType: 'system',
                    }}
                  >
                    <div className={styles.userInfoSummary}>
                      <div className={styles.userInfoActions}>
                        <div className={styles.actionButtons}>
                          <Button
                            type={ButtonType.PRIMARY}
                            color={ButtonColor.GREEN_PRIMARY}
                            size={ButtonSize.BASE}
                            onClick={() => setIsUserInfoModalOpen(true)}
                            state={ButtonState.DEFAULT}
                          >
                            Enter details
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Message>
                ) : (
                  <>
                    <Message
                      key="user-info-summary-message"
                      message={{
                        id: -999, // Stable negative ID to avoid conflicts with real messages
                        content: 'This is what I already know about you and your place:',
                        documents: [],
                        type: MessageTypeValue.Text,
                        timestamp: '2024-01-01T00:00:00.000Z', // Stable timestamp
                        senderType: 'system',
                      }}
                    >
                      <div className={styles.userInfoSummary}>
                        <div className={styles.userInfoContainer}>
                          <UserInfoModal
                            open={false}
                            onClose={() => {}}
                            readOnly={true}
                            inline={true}
                            renderToBody={false}
                          />
                        </div>
                        <div className={styles.userInfoActions}>
                          <p className={styles.confirmationQuestion}>
                            Are your current details correct?
                          </p>
                          {isValidationErrorShown && (
                            <div className={styles.error}>
                              Please make sure you have provided all the required details and
                              verified your email and phone number. If you keep seeing this message,
                              please{' '}
                              <a
                                href="https://help.heyalfie.com/help-centre/kb-tickets/new"
                                target="_blank"
                                rel="noopener noreferrer"
                                className={styles.link}
                              >
                                contact support
                              </a>
                            </div>
                          )}
                          <div className={styles.actionButtons}>
                            <Button
                              type={ButtonType.PRIMARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={handleDetailsLookGood}
                              loading={isValidatingDetails}
                              state={
                                shouldDisableItLooksGood
                                  ? ButtonState.DISABLED
                                  : ButtonState.DEFAULT
                              }
                            >
                              It looks good
                            </Button>
                            <Button
                              type={ButtonType.SECONDARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={() => setIsUserInfoModalOpen(true)}
                              state={
                                shouldDisableButtons ? ButtonState.DISABLED : ButtonState.DEFAULT
                              }
                            >
                              Edit securely
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Message>
                    {showItLooksGoodMessage && (
                      <Message
                        key="it-looks-good-fake-message"
                        message={{
                          id: -1000,
                          content: 'It looks good',
                          documents: [],
                          type: MessageTypeValue.Text,
                          timestamp: new Date().toISOString(),
                          senderType: 'user',
                        }}
                        className={styles.fakeMessage}
                      />
                    )}
                  </>
                )}

                {(userDetailsValidated || !isLastMessage) && (
                  <NotificationTagManager
                    messageId={message.id}
                    messageNotificationTags={activeNotificationTags}
                    messages={messages}
                  >
                    {(showNotificationTag) => (
                      <Message
                        message={message}
                        messages={messages}
                        showNotificationTag={showNotificationTag}
                      />
                    )}
                  </NotificationTagManager>
                )}
                {jobSummaryConfirmed && userDetailsValidated && (
                  <>
                    <Message
                      key="__yes_looks_good__"
                      message={{
                        id: -1001,
                        content: 'Yes, that looks good',
                        documents: [],
                        type: MessageTypeValue.Text,
                        timestamp: new Date().toISOString(),
                        senderType: 'user',
                      }}
                    />
                    <Message
                      key="__job_confirmed_summary__"
                      message={{
                        id: -1002,
                        content: `Your job is confirmed, you can now sit back and relax.&nbsp;  
                          With Hey Alfie:
                          &nbsp;&nbsp;&nbsp;&nbsp;• You're connected with trusted professionals only
                          &nbsp;&nbsp;&nbsp;&nbsp;• All jobs are backed by our **£1,000 Hey Alfie Guarantee** (T&Cs apply)
                          &nbsp;&nbsp;&nbsp;&nbsp;• We handle everything end-to-end for your peace of mind`,

                        type: MessageTypeValue.Text,
                        timestamp: new Date().toISOString(),
                        senderType: 'system',
                      }}
                    >
                      <div className={styles.userInfoSummary}>
                        <div className={styles.userInfoActions}>
                          <div
                            className={`${styles.actionButtons} ${styles.newChatButtonContainer}`}
                          >
                            <Button
                              type={ButtonType.PRIMARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={handleStartNewChat}
                              state={ButtonState.DEFAULT}
                            >
                              Start new chat
                            </Button>
                          </div>
                          <div className={styles.actionButtons}>
                            <Button
                              type={ButtonType.SECONDARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={() =>
                                handleNavigateToNewChatWithMessage('Find me a tradesperson')
                              }
                              state={ButtonState.DEFAULT}
                            >
                              Find me a tradesperson
                            </Button>
                            <Button
                              type={ButtonType.SECONDARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={() =>
                                handleNavigateToNewChatWithMessage('Help with an issue')
                              }
                              state={ButtonState.DEFAULT}
                            >
                              Help with an issue
                            </Button>
                          </div>
                          <div className={styles.actionButtons}>
                            <Button
                              type={ButtonType.SECONDARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={() =>
                                handleNavigateToNewChatWithMessage('DIY instruction and support')
                              }
                              state={ButtonState.DEFAULT}
                            >
                              DIY instruction and support
                            </Button>
                            <Button
                              type={ButtonType.SECONDARY}
                              color={ButtonColor.GREEN_PRIMARY}
                              size={ButtonSize.BASE}
                              onClick={() =>
                                handleNavigateToNewChatWithMessage('Research home improvement')
                              }
                              state={ButtonState.DEFAULT}
                            >
                              Research home improvement
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Message>
                  </>
                )}
              </div>
            );
          }

          return (
            <div key={index}>
              <NotificationTagManager
                messageId={message.id}
                messageNotificationTags={activeNotificationTags}
                messages={messages}
              >
                {(showNotificationTag) => (
                  <Message
                    message={message}
                    messages={messages}
                    showNotificationTag={showNotificationTag}
                  />
                )}
              </NotificationTagManager>
            </div>
          );
        })}

        {messages.length > 0 && (
          <InfoBox
            className="mb-4"
            title="Did you know, you can:"
            items={[
              {
                icon: (
                  <HugeiconsIcon
                    icon={BubbleChatIcon as unknown as IconSvgObject}
                    size={16}
                    className="text-black"
                  />
                ),
                text: 'Chat or select options',
              },
              {
                icon: (
                  <HugeiconsIcon
                    icon={Mic02Icon as unknown as IconSvgObject}
                    size={16}
                    className="text-black"
                  />
                ),
                text: 'Use voice or upload photos',
              },
              {
                icon: (
                  <HugeiconsIcon
                    icon={ArrowReloadHorizontalIcon as unknown as IconSvgObject}
                    size={16}
                    className="text-black"
                  />
                ),
                text: 'Change topic whenever you like',
              },
            ]}
          />
        )}

        {hasMore && <div ref={sentinelRef} style={{ height: 20 }} />}
      </div>
      {!isGuestFlow ? (
        <UserInfoModal
          renderToBody
          open={isUserInfoModalOpen}
          onClose={() => setIsUserInfoModalOpen(false)}
        />
      ) : (
        <GuestUserInfoModal
          open={isUserInfoModalOpen}
          onClose={() => {
            setIsUserInfoModalOpen(false);
            isSignedIn && setIsGuestFlow(false);
          }}
          onFinish={() => {
            setIsUserInfoModalOpen(false);
            setIsGuestFlow(false);
            checkJobSummaryStatus();
          }}
        />
      )}

      <div className={styles.scrollAnchor}></div>
    </div>
  );
};
