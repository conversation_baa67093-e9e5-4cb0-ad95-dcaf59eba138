.messagesContainer {
  display: flex;
  flex-direction: column-reverse;
  height: 100%;
  overflow-y: auto;
  padding: 32px 0 24px;
  position: relative;
  z-index: 1;

  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;

  &::-webkit-scrollbar {
    width: 0;
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
}

.messagesContainer * {
  overflow-anchor: none;
}

.scrollAnchor {
  height: 1px;
  overflow-anchor: auto;
}

.messagesWrapper {
  display: flex;
  flex-direction: column-reverse;
  margin-top: auto;
  position: relative;
  z-index: 1;
}

.addressInputContainer {
  display: flex;
  flex-direction: column;
  gap: 14px;
  max-width: 360px;
  padding-top: 14px;
}

.editUserInfoButton {
  max-width: 360px;
  padding-top: 14px;
}

.userInfoSummary {
  margin-top: 16px;
}

.userInfoContainer {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.userInfoActions {
  margin-top: 24px;

  .actionButtons + .actionButtons {
    margin-top: 12px;
  }
}

.confirmationQuestion {
  font-size: 16px;
  font-weight: 500;
  color: var(--colors-black);
  margin-bottom: 16px;
}

.actionButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.newChatButtonContainer {
  width: 193px;
  display: flex;

  > * {
    flex: 1;
  }
}

.fakeMessage {
  order: 0;
}

.error {
  font-size: 14px;
  color: var(--colors-red-500);
  margin-top: 4px;
  margin-bottom: 8px;
}

.link {
  text-decoration: underline;

  &:hover {
    text-decoration: none;
  }

  margin-left: 50px;
}
