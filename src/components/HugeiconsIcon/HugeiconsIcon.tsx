import React from "react";
import { HugeiconsIconProps } from "./HugeiconsIcon.types";
import styles from "./HugeiconsIcon.module.scss";
import classNames from "classnames";
import UniversalIcon from "../UniversalIcon/UniversalIcon";
import { convertIconToPaths } from "../Button";

export const HugeiconsIcon = ({
  icon,
  size = 24,
  color = "currentColor",
  strokeWidth = 0,
  className,
  onClick,
}: HugeiconsIconProps): JSX.Element => {
  const paths = convertIconToPaths(icon);

  return (
    <div
      className={classNames(
        styles.icon,
        onClick && styles.clickable,
        className
      )}
      onClick={onClick}
    >
      <UniversalIcon
        paths={paths}
        width={size}
        height={size}
        color={color}
        strokeWidth={strokeWidth}
      />
    </div>
  );
};
