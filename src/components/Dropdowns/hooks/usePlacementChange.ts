import { useState, useEffect, RefObject, useCallback } from 'react';

interface UsePlacementChangeProps {
  menuRef: RefObject<HTMLDivElement>;
  defaultPlacement: 'left' | 'right';
}

export const usePlacementChange = ({ 
  menuRef, 
  defaultPlacement 
}: UsePlacementChangeProps) => {
  const [placement, setPlacement] = useState(defaultPlacement);

  const calculatePlacement = useCallback(() => {
    if (!menuRef.current) return;

    const menuRect = menuRef.current.getBoundingClientRect();
    const windowWidth = window.innerWidth;

    if (defaultPlacement === 'left' && menuRect.right > windowWidth - 8) {
      setPlacement('right');
    }
    else if (defaultPlacement === 'right' && menuRect.left < 8) {
      setPlacement('left');
    }
    else {
      setPlacement(defaultPlacement);
    }
  }, [menuRef, defaultPlacement]);

  useEffect(() => {
    calculatePlacement();
  }, [calculatePlacement]);

  return { placement, calculatePlacement };
}; 