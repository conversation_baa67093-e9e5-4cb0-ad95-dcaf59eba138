'use client';

import * as React from 'react';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from './ui/calendar';
import { Button } from './ui/button';
import { format } from 'date-fns';

export const DatePicker = ({
  value,
  onChange,
}: {
  value?: Date;
  onChange?: (date: Date) => void;
}) => {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (date: Date | undefined) => {
    if (date) {
      onChange?.(date);
      setOpen(false);
    }
  };

  return (
    <Popover modal open={open} onOpenChange={setOpen}>
      <PopoverTrigger>
        <Button
          variant="outline"
          data-empty={!value}
          type="button"
          className="data-[empty=true]:text-muted-foreground w-full justify-start text-left font-normal rounded-lg py-3 px-2 text-base !leading-[24px] border focus:border-gray-500"
        >
          {value ? format(value, 'dd/MM/yyyy') : 'Pick a date'}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-auto p-0 z-[9999]">
        <Calendar
          required
          mode="single"
          selected={value}
          onSelect={handleSelect}
          disabled={{ before: new Date() }}
        />
      </PopoverContent>
    </Popover>
  );
};
