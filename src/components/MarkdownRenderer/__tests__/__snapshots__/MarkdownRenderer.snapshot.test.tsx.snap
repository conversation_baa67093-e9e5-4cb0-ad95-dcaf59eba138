// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`MarkdownRenderer Snapshots > matches default markdown snapshot 1`] = `
<div>
  <div
    class="_markdownWrapper_da0ea5 test-class"
  >
    <h1>
      Test Content
    </h1>
  </div>
</div>
`;

exports[`MarkdownRenderer Snapshots > matches snapshot with empty content 1`] = `
<div>
  <div
    class="_markdownWrapper_da0ea5 "
  />
</div>
`;

exports[`MarkdownRenderer Snapshots > matches snapshot with formatted text 1`] = `
<div>
  <div
    class="_markdownWrapper_da0ea5 "
  >
    <pre>
      <pre>
        <code
          node="[object Object]"
        >
            # Heading 1
  ## Heading 2
  
  **Bold text**
  *Italic text*
  
  - List item 1
  - List item 2

        </code>
      </pre>
    </pre>
  </div>
</div>
`;

exports[`MarkdownRenderer Snapshots > matches snapshot with table content 1`] = `
<div>
  <div
    class="_markdownWrapper_da0ea5 "
  >
    <pre>
      <pre>
        <code
          node="[object Object]"
        >
            | Header 1 | Header 2 |
  |----------|----------|
  | Cell 1   | Cell 2   |

        </code>
      </pre>
    </pre>
  </div>
</div>
`;

exports[`MarkdownRenderer Snapshots > matches snapshot without className 1`] = `
<div>
  <div
    class="_markdownWrapper_da0ea5 "
  >
    <h1>
      Test Content
    </h1>
  </div>
</div>
`;
