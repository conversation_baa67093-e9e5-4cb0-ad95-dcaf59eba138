import React, { useRef, useState, useEffect, useCallback } from 'react';
import classNames from 'classnames';
import { IconSvgObject } from '@hugeicons/react';
import { House04Icon } from '@hugeicons-pro/core-stroke-standard';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { AddressRoleForm } from '../AddressRoleForm';
import { PropertyHeaderProps, PropertyHeaderState } from './PropertyHeader.types';
import styles from './PropertyHeader.module.scss';
import { Modal } from '../Modal';
import { useAuth } from '@clerk/nextjs';
import { useAssistanceModal } from '@/hooks/useAssistanceModal';
import { AddressType } from '@/components/AddressInput';

export const PropertyHeader: React.FC<PropertyHeaderProps> = ({
  address,
  ownerStatus,
  profileImageUrl,
  state = PropertyHeaderState.EMPTY,
  onAddAddress,
  onEditAddress,
  onProfileImageChange,
  onAddressComplete,
  className,
  children,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [localAddress, setLocalAddress] = useState(address);
  const [localOwnerStatus, setLocalOwnerStatus] = useState(ownerStatus);
  const [localState, setLocalState] = useState(state);
  const [localProfileImageUrl, setLocalProfileImageUrl] = useState(profileImageUrl);
  const [tempAddress, setTempAddress] = useState<AddressType>();
  const [tempRole, setTempRole] = useState<string | null>(null);
  const { isSignedIn } = useAuth();
  const { openModal } = useAssistanceModal();

  useEffect(() => {
    setLocalAddress(address);
    setLocalOwnerStatus(ownerStatus);
    setLocalState(state);
    setLocalProfileImageUrl(profileImageUrl);
  }, [address, ownerStatus, state, profileImageUrl]);

  useEffect(() => {
    return () => {
      if (localProfileImageUrl && localProfileImageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(localProfileImageUrl);
      }
    };
  }, [localProfileImageUrl]);

  const isEmpty = localState === PropertyHeaderState.EMPTY || !localAddress;

  const headerClasses = classNames(
    styles.propertyHeader,
    {
      [styles.empty]: isEmpty,
      [styles.filled]: !isEmpty,
    },
    className
  );

  const handleButtonClick = () => {
    if (!isSignedIn) {
      openModal('please-sign-up');
      return;
    }
    if (isEmpty) {
      setIsModalOpen(true);
      setTempAddress(undefined);
      setTempRole(null);
      onAddAddress?.();
    } else if (!isEmpty && onEditAddress) {
      onEditAddress();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setLocalProfileImageUrl(imageUrl);

      if (onProfileImageChange) {
        onProfileImageChange(file);
      }
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRoleChange = useCallback((role: string) => {
    setTempRole(role);
  }, []);

  const handleSave = useCallback(() => {
    if (tempAddress && tempRole) {
      setLocalAddress('label' in tempAddress
        ? tempAddress.label
        : `${tempAddress.line1}, ${tempAddress.city}, ${tempAddress.postcode}`.trim());

      let ownerStatus = '';
      switch (tempRole) {
        case 'owner':
          ownerStatus = 'Owner and occupier';
          break;
        case 'landlord':
          ownerStatus = 'Landlord';
          break;
        case 'tenant':
          ownerStatus = 'Tenant';
          break;
        default:
          ownerStatus = tempRole;
      }

      setLocalOwnerStatus(ownerStatus);
      setLocalState(PropertyHeaderState.FILLED);
      setIsModalOpen(false);

      onAddressComplete?.({
        address: tempAddress,
        role: tempRole,
      });
    }
  }, [onAddressComplete, tempAddress, tempRole]);

  const isFormValid = () => {
    return tempRole && tempAddress;
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const renderIcon = () => {
    if (localProfileImageUrl) {
      // eslint-disable-next-line @next/next/no-img-element
      return <img src={localProfileImageUrl} alt="Profile" className={styles.profileImage} />;
    }
    return (
      // eslint-disable-next-line @next/next/no-img-element
      <img src="/profileDefault.png" alt="Default Profile" className={styles.defaultProfileImage} />
    );
  };

  return (
    <>
      <div className={headerClasses}>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          style={{ display: 'none' }}
          onChange={handleFileChange}
        />

        {isEmpty ? (
          <div className={styles.emptyContent}>
            <div className={styles.iconSection}>
              <div className={styles.iconContainer}>{renderIcon()}</div>
            </div>

            <div className={styles.rightContent}>
              <div className={styles.textSection}>
                <span className={styles.title}>Your address</span>
              </div>

              <div className={styles.buttonSection}>
                <Button
                  type={ButtonType.PRIMARY}
                  color={ButtonColor.GREEN_PRIMARY}
                  size={ButtonSize.BASE}
                  onClick={handleButtonClick}
                  className={styles.actionButton}
                >
                  Add address
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className={styles.filledContent}>
            <div className={styles.iconSection}>
              <div className={styles.iconContainer}>{renderIcon()}</div>
            </div>

            <div className={styles.rightContent}>
              <div className={styles.headerLine}>
                <span className={styles.title}>Your address</span>
                {localOwnerStatus && <span className={styles.ownerBadge}>{localOwnerStatus}</span>}
              </div>
              <div className={styles.addressLine}>
                <div className={styles.homeIcon}>
                  <HugeiconsIcon icon={House04Icon as unknown as IconSvgObject} size={24} />
                </div>
                <span className={styles.address}>{localAddress}</span>
              </div>
            </div>
          </div>
        )}

        {children}
      </div>

      {isModalOpen && (
        <Modal
          open={isModalOpen}
          onClose={handleModalClose}
          title="Your address"
          actionButtons={
            <Button
              onClick={handleSave}
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={!isFormValid() ? ButtonState.DISABLED : ButtonState.DEFAULT}
              disabled={!isFormValid()}
            >
              Save
            </Button>
          }
        >
          <AddressRoleForm
            selectedRole={tempRole}
            onAddressChange={setTempAddress}
            onRoleChange={handleRoleChange}
            forceDropdownPosition="bottom"
          />
        </Modal>
      )}
    </>
  );
};
