import { ReactNode } from 'react';
import { AddressType } from '@/components/AddressInput';

export enum PropertyHeaderState {
  EMPTY = 'empty',
  FILLED = 'filled',
}

export interface PropertyHeaderProps {
  address?: string;
  ownerStatus?: string;
  profileImageUrl?: string;
  state?: PropertyHeaderState;
  onAddAddress?: () => void;
  onEditAddress?: () => void;
  onProfileImageChange?: (file: File) => void;
  onAddressComplete?: (data: { address: AddressType; role: string }) => void;
  className?: string;
  children?: ReactNode;
}
