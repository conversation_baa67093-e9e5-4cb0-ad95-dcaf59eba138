import type { Meta, StoryObj } from '@storybook/react';
import { PropertyHeader } from './PropertyHeader';
import { PropertyHeaderState } from './PropertyHeader.types';

const meta: Meta<typeof PropertyHeader> = {
  title: 'Components/PropertyHeader',
  component: PropertyHeader,
  parameters: {
    layout: 'padded',
  },
  argTypes: {
    onAddAddress: { action: 'onAddAddress' },
    onEditAddress: { action: 'onEditAddress' },
    onProfileImageChange: { action: 'onProfileImageChange' },
  },
};

export default meta;
type Story = StoryObj<typeof PropertyHeader>;

export const Empty: Story = {
  args: {
    state: PropertyHeaderState.EMPTY,
    onAddAddress: () => {
      console.log('Add address clicked - opens Onboarding in addAddressMode');
    },
    onProfileImageChange: (file: File) => {
      console.log('Selected file:', file.name);
    },
  },
};

export const Filled: Story = {
  args: {
    state: PropertyHeaderState.FILLED,
    address: '6 Bishops Road, SW6 7AB',
    ownerStatus: 'Owner and occupier',
    onEditAddress: () => {
      console.log('Edit address clicked');
    },
    onProfileImageChange: (file: File) => {
      console.log('Selected file:', file.name);
    },
  },
};

export const WithProfileImage: Story = {
  args: {
    state: PropertyHeaderState.FILLED,
    address: '6 Bishops Road, SW6 7AB',
    ownerStatus: 'Owner and occupier',
    profileImageUrl:
      'https://images.unsplash.com/photo-1568605114967-8130f3a36994?w=400&h=400&fit=crop&crop=face',
    onEditAddress: () => {
      console.log('Edit address clicked');
    },
    onProfileImageChange: (file: File) => {
      console.log('Selected file:', file.name);
    },
  },
};
