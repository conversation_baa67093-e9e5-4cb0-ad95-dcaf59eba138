import React, { ReactNode, useRef, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import clsx from 'clsx';
import styles from './Modal.module.scss';
import { IconSvgObject } from '@hugeicons/react';
import { MultiplicationSignIcon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { useBreakpoint } from '@/utils/breakpointUtils';

interface ModalProps extends React.HTMLAttributes<HTMLDivElement> {
  open: boolean;
  onClose: () => void;
  children: ReactNode;
  actionButtons?: ReactNode;
  title?: string;
  closeButtonPosition?: 'left' | 'right';
}

export const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  children,
  actionButtons,
  title,
  closeButtonPosition = 'right',
  ...props
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useBreakpoint();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Block body scroll when modal is open
  useEffect(() => {
    if (open && mounted) {
      const originalOverflow = document.body.style.overflow;
      const originalPaddingRight = document.body.style.paddingRight;
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

      document.body.classList.add('modal-open');
      document.body.style.overflow = 'hidden';
      if (scrollbarWidth > 0) {
        document.body.style.paddingRight = `${scrollbarWidth}px`;
      }

      return () => {
        document.body.classList.remove('modal-open');
        document.body.style.overflow = originalOverflow;
        document.body.style.paddingRight = originalPaddingRight;
      };
    }
  }, [open, mounted]);

  // Handle ESC key to close modal
  useEffect(() => {
    if (open && mounted) {
      const handleEscKey = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          onClose();
        }
      };

      document.addEventListener('keydown', handleEscKey);
      return () => {
        document.removeEventListener('keydown', handleEscKey);
      };
    }
  }, [open, mounted, onClose]);

  if (!open || !mounted) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const modalContent = (
    <div className={styles.overlay} onClick={handleOverlayClick}>
      <div
        className={clsx(styles.modal, { [styles.mobile]: isMobile })}
        role="dialog"
        aria-modal="true"
        {...props}
      >
        <div className={styles.modalInner}>
          <div className={styles.header}>
            {title && <h2 className={styles.title}>{title}</h2>}
            <HugeiconsIcon
              icon={MultiplicationSignIcon as unknown as IconSvgObject}
              size={24}
              className={clsx({
                [styles.closeButtonRight]: closeButtonPosition === 'right',
                [styles.closeButtonLeft]: closeButtonPosition === 'left',
              })}
              onClick={onClose}
            />
          </div>
          <div className={styles.content} ref={contentRef}>
            {children}
          </div>
        </div>
        {actionButtons && <div className={styles.actions}>{actionButtons}</div>}
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};
