// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Radio Snapshots > matches checked radio snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _normal_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        checked=""
        class="_radio_31042b _checked_31042b"
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
        <div
          class="_caption_31042b"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Radio Snapshots > matches checked state snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _checked_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        class="_radio_31042b"
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
        <div
          class="_caption_31042b _checked_31042b"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Radio Snapshots > matches default radio snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _normal_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        class="_radio_31042b"
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
        <div
          class="_caption_31042b"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Radio Snapshots > matches disabled state snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _disabled_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        class="_radio_31042b _disabled_31042b"
        disabled=""
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
        <div
          class="_caption_31042b _disabled_31042b"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Radio Snapshots > matches error state snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _error_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        class="_radio_31042b _error_31042b"
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
        <div
          class="_caption_31042b _error_31042b"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Radio Snapshots > matches normal state snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _normal_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        class="_radio_31042b"
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
        <div
          class="_caption_31042b"
        >
          Test Helper Text
        </div>
      </div>
    </div>
  </label>
</div>
`;

exports[`Radio Snapshots > matches radio with error text snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _error_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        class="_radio_31042b _error_31042b"
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
        <div
          class="_caption_31042b _error_31042b"
        >
          Test Helper Text
        </div>
      </div>
    </div>
    <div
      class="_error-container_31042b"
    >
      <svg
        class="_error-icon_31042b"
        color="currentColor"
        fill="none"
        height="16"
        viewBox="0 0 24 24"
        width="16"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="1.5"
        />
        <path
          d="M11.992 15H12.001"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
        />
        <path
          d="M12 12L12 8"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.5"
        />
      </svg>
      <span
        class="_error-text_31042b"
      >
        Test Error Text
      </span>
    </div>
  </label>
</div>
`;

exports[`Radio Snapshots > matches radio without helper text snapshot 1`] = `
<div>
  <label
    class="_radio-field_31042b _normal_31042b"
  >
    <div
      class="_content_31042b"
    >
      <input
        class="_radio_31042b"
        type="radio"
        value=""
      />
      <div
        class="_label-helper_31042b"
      >
        <div
          class="_label_31042b"
        >
          Test Label
        </div>
      </div>
    </div>
  </label>
</div>
`;
