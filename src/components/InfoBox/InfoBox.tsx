import React from 'react';
import clsx from 'clsx';

import { InfoBoxProps } from './InfoBox.types';

export const InfoBox: React.FC<InfoBoxProps> = ({
  title = 'Did you know, you can:',
  items,
  className,
}) => {
  return (
    <div
      className={clsx('relative border border-yellow-600 p-4 pt-6 rounded-lg bg-white', className)}
    >
      <div className="absolute top-0 left-4 -translate-y-1/2 bg-yellow-50 px-2 py-1 border border-yellow-600 rounded-md text-sm font-bold">
        {title}
      </div>
      <ul className="flex flex-col gap-3">
        {items.map((item, index) => (
          <li key={index} className="flex gap-2 items-center">
            {item.icon}
            <div className="text-sm text-black leading-relaxed">{item.text}</div>
          </li>
        ))}
      </ul>
    </div>
  );
};
