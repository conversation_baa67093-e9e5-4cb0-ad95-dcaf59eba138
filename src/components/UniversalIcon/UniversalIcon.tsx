import React from 'react';

interface SvgProps extends React.SVGProps<SVGSVGElement> {
  paths: Array<[string, React.SVGProps<SVGPathElement>]>;
}

const UniversalIcon: React.FC<SvgProps> = ({ paths, ...svgProps }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" {...svgProps}>
    {paths.map(([element, props], index) => {
      if (element === 'path') {
        return <path {...props} key={props.key || index} stroke="currentColor" />;
      }
      // Add more SVG elements if needed
      return null;
    })}
  </svg>
);

export default UniversalIcon;
