import React from 'react';
import { PersonalizationCardFile } from '@/components/PersonalizationCard/PersonalizationCard.types';
import { Spinner } from '@/components/Spinner';
import styles from './UploadPersonalizationCard.module.scss';
import { getUserFriendlyType } from '@/api/documents';

interface UploadPersonalizationCardProps {
  file: PersonalizationCardFile;
  compact?: boolean;
}

export const UploadPersonalizationCard: React.FC<UploadPersonalizationCardProps> = ({
  file,
  compact = false,
}) => {
  if (compact) {
    return (
      <div className={styles.compactItem}>
        <Spinner color="var(--colors-yellow-500)" size={24} />
      </div>
    );
  }

  return (
    <div className={styles.fileItem}>
      <div className={styles.loadingIcon}>
        <Spinner color="var(--colors-yellow-500)" size={18} />
      </div>
      <div className={styles.fileInfo}>
        <div className={styles.fileName}>{file.fileName}</div>
        <div className={styles.fileType}>{getUserFriendlyType(file)}</div>
      </div>
    </div>
  );
};
