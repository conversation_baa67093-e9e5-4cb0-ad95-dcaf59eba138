.fileItem {
  display: flex;
  align-items: center;
  background: var(--colors-gray-200);
  border-radius: 12px;
  padding: 16px;
  gap: 16px;
  max-width: 315px;
  width: 100%;
  height: 68px;
}

.compactItem {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--colors-gray-200);
  border-radius: 12px;
  width: 68px;
}

.loadingIcon {
  width: 44px;
  height: 44px;
  background: #cfc9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fileInfo {
  flex: 1;
  min-width: 0;
}

.fileName {
  font-size: 15px;
  font-weight: 500;
  color: #000000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
  margin-bottom: 4px;
}

.fileType {
  font-size: 13px;
  color: #4b5563;
}

@media (max-width: 768px) {
  .fileItem {
    max-width: 100%;
  }

  .compactItem {
    width: 60px;
    height: 60px;
  }
}
