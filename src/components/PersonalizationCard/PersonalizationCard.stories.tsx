import type { <PERSON>a, StoryObj } from '@storybook/react';
import { PersonalizationCard } from './PersonalizationCard';
import { UploadPersonalizationCard } from './components/UploadPersonalizationCard';
import { PersonalizationCardField, PersonalizationCardFile } from './PersonalizationCard.types';
import React from 'react';

const meta: Meta<typeof PersonalizationCard> = {
  title: 'Components/PersonalizationCard',
  component: PersonalizationCard,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof PersonalizationCard>;

const mockFields: PersonalizationCardField[] = [
  {
    id: '1',
    label: 'Brand',
    value: 'Samsung',
    editable: false,
    type: 'text',
    placeholder: 'e.g. Samsung',
  },
  {
    id: '2',
    label: 'Model',
    value: 'RB38T675DB1',
    editable: false,
    type: 'text',
    placeholder: 'Type your fridge model number',
  },
  {
    id: '3',
    label: 'Serial Number',
    value: 'SN123456789',
    editable: true,
    type: 'text',
    placeholder: 'Type your serial number',
  },
  {
    id: '4',
    label: 'Warranty',
    value: '1 year',
    editable: true,
    type: 'text',
    placeholder: 'e.g. 5 years',
  },
];

const mockFiles: PersonalizationCardFile[] = [
  {
    id: 1,
    fileName: 'Fridge - freezer .PDF',
    browserMimeType: 'application/pdf',
    status: 'processingCompleted',
    sizeInKiloBytes: 100,
    createdAt: '2024',
    uploadContext: 'filesPage',
  },
];

export const Default: Story = {
  render: (args) => {
    const [isEditing, setIsEditing] = React.useState(false);
    const [fields, setFields] = React.useState(args.fields);

    return (
      <PersonalizationCard
        {...args}
        fields={fields}
        isEditing={isEditing}
        onEdit={() => setIsEditing(true)}
        onCancel={() => setIsEditing(false)}
        onSave={(updatedFields) => {
          setFields(updatedFields);
          setIsEditing(false);
        }}
        onFileRemove={args.onFileRemove}
      />
    );
  },
  args: {
    fields: mockFields,
  },
};

export const WithFixedWidth: Story = {
  render: (args) => {
    const [isEditing, setIsEditing] = React.useState(false);
    const [fields, setFields] = React.useState(args.fields);

    return (
      <div style={{ width: '798px' }}>
        <PersonalizationCard
          {...args}
          fields={fields}
          isEditing={isEditing}
          onEdit={() => setIsEditing(true)}
          onCancel={() => setIsEditing(false)}
          onSave={(updatedFields) => {
            setFields(updatedFields);
            setIsEditing(false);
          }}
          onFileRemove={args.onFileRemove}
        />
      </div>
    );
  },
  args: {
    fields: mockFields,
  },
};

export const WithFile: Story = {
  render: (args) => {
    const [isEditing, setIsEditing] = React.useState(false);
    const [fields, setFields] = React.useState(args.fields);
    const [files, setFiles] = React.useState(args.files);

    return (
      <PersonalizationCard
        {...args}
        fields={fields}
        files={files}
        isEditing={isEditing}
        onEdit={() => setIsEditing(true)}
        onCancel={() => setIsEditing(false)}
        onSave={(updatedFields, updatedFiles) => {
          setFields(updatedFields);
          setFiles(updatedFiles);
          setIsEditing(false);
        }}
        onFileRemove={(fileId) => {
          if (files) {
            setFiles(files.filter((file) => file.id !== fileId));
          }
        }}
      />
    );
  },
  args: {
    fields: mockFields,
    files: mockFiles,
  },
};

export const UploadCard: Story = {
  render: () => {
    return <UploadPersonalizationCard file={mockFiles[0]} />;
  },
};
