// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`PersonalizationCard Snapshots > matches default snapshot 1`] = `
<div>
  <div
    class="_container_bffe77  "
  >
    <div
      class="_content_bffe77"
    >
      <div
        class="_header_bffe77"
      >
        <h2
          class="_title_bffe77"
        >
          Fridge Freezer
        </h2>
      </div>
      <div
        class="_fieldsContainer_bffe77"
      >
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Brand
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              Bosch
            </span>
          </div>
        </div>
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Model
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              12345677
            </span>
          </div>
        </div>
      </div>
      <div
        class="_filesSection_bffe77"
      >
        <div
          class="_documentContainer_fd77b0 _documentContainerDark_fd77b0 _documentContainerShort_fd77b0"
        >
          <div
            class="_documentPreviewIconContainerShort_fd77b0"
          >
            <div
              class="_documentPreviewIcon_fd77b0"
            >
              <div
                class="_icon_053e64"
              >
                <svg
                  color="#fff"
                  fill="none"
                  height="20"
                  stroke-width="0"
                  viewBox="0 0 24 24"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 10L20 20"
                    stroke="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div
              class="_documentContent_fd77b0"
            >
              <p
                class="body-s quasimoda _ellipsis_fd77b0 _documentName_fd77b0"
              >
                Fridge - freezer...
              </p>
              <p
                class="body-xss quasimoda _ellipsis_fd77b0 _documentDescription_fd77b0"
              >
                PDF
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="_divider_bffe77"
    />
    <div
      class="_actions_bffe77"
    >
      <button
        data-testid="mock-button"
      >
        Edit details
      </button>
    </div>
  </div>
</div>
`;

exports[`PersonalizationCard Snapshots > matches editing mode snapshot 1`] = `
<div>
  <div
    class="_container_bffe77  "
  >
    <div
      class="_content_bffe77"
    >
      <div
        class="_header_bffe77"
      >
        <div
          class="_input-field_9af53b _normal_9af53b _fullWidth_9af53b _titleInputWrapper_bffe77"
        >
          <div
            class="_input_9af53b _normal_9af53b _regular_9af53b _titleInput_bffe77"
          >
            <div
              class="_content_9af53b"
            >
              <input
                class="_input-text_9af53b"
                placeholder="Input text"
                type="text"
                value="Fridge Freezer"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="_fieldsContainer_bffe77"
      >
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Brand
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <div
              class="_input-field_9af53b _normal_9af53b _inputWrapper_bffe77"
            >
              <div
                class="_input_9af53b _normal_9af53b _regular_9af53b _input_bffe77 "
              >
                <div
                  class="_content_9af53b"
                >
                  <input
                    class="_input-text_9af53b"
                    placeholder="Enter brand"
                    type="text"
                    value="Bosch"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Model
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <div
              class="_input-field_9af53b _normal_9af53b _inputWrapper_bffe77"
            >
              <div
                class="_input_9af53b _normal_9af53b _regular_9af53b _input_bffe77 "
              >
                <div
                  class="_content_9af53b"
                >
                  <input
                    class="_input-text_9af53b"
                    placeholder="Enter model"
                    type="text"
                    value="12345677"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="_filesSection_bffe77"
      >
        <div
          class="_documentContainer_fd77b0 _documentContainerDark_fd77b0 _documentContainerShort_fd77b0"
        >
          <div
            class="_documentPreviewIconContainerShort_fd77b0"
          >
            <div
              class="_documentPreviewIcon_fd77b0"
            >
              <div
                class="_icon_053e64"
              >
                <svg
                  color="#fff"
                  fill="none"
                  height="20"
                  stroke-width="0"
                  viewBox="0 0 24 24"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 10L20 20"
                    stroke="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div
              class="_documentContent_fd77b0"
            >
              <p
                class="body-s quasimoda _ellipsis_fd77b0 _documentName_fd77b0"
              >
                Fridge - freezer...
              </p>
              <p
                class="body-xss quasimoda _ellipsis_fd77b0 _documentDescription_fd77b0"
              >
                PDF
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="_divider_bffe77"
    />
    <div
      class="_actions_bffe77"
    >
      <button
        data-testid="mock-button"
        disabled=""
      >
        Save
      </button>
      <button
        data-testid="mock-button"
      >
        Cancel
      </button>
    </div>
  </div>
</div>
`;

exports[`PersonalizationCard Snapshots > matches snapshot with custom className 1`] = `
<div>
  <div
    class="_container_bffe77  custom-class"
  >
    <div
      class="_content_bffe77"
    >
      <div
        class="_header_bffe77"
      >
        <h2
          class="_title_bffe77"
        >
          Fridge Freezer
        </h2>
      </div>
      <div
        class="_fieldsContainer_bffe77"
      >
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Brand
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              Bosch
            </span>
          </div>
        </div>
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Model
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              12345677
            </span>
          </div>
        </div>
      </div>
      <div
        class="_filesSection_bffe77"
      >
        <div
          class="_documentContainer_fd77b0 _documentContainerDark_fd77b0 _documentContainerShort_fd77b0"
        >
          <div
            class="_documentPreviewIconContainerShort_fd77b0"
          >
            <div
              class="_documentPreviewIcon_fd77b0"
            >
              <div
                class="_icon_053e64"
              >
                <svg
                  color="#fff"
                  fill="none"
                  height="20"
                  stroke-width="0"
                  viewBox="0 0 24 24"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 10L20 20"
                    stroke="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div
              class="_documentContent_fd77b0"
            >
              <p
                class="body-s quasimoda _ellipsis_fd77b0 _documentName_fd77b0"
              >
                Fridge - freezer...
              </p>
              <p
                class="body-xss quasimoda _ellipsis_fd77b0 _documentDescription_fd77b0"
              >
                PDF
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="_divider_bffe77"
    />
    <div
      class="_actions_bffe77"
    >
      <button
        data-testid="mock-button"
      >
        Edit details
      </button>
    </div>
  </div>
</div>
`;

exports[`PersonalizationCard Snapshots > matches snapshot with multiple files 1`] = `
<div>
  <div
    class="_container_bffe77  "
  >
    <div
      class="_content_bffe77"
    >
      <div
        class="_header_bffe77"
      >
        <h2
          class="_title_bffe77"
        >
          Fridge Freezer
        </h2>
      </div>
      <div
        class="_fieldsContainer_bffe77"
      >
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Brand
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              Bosch
            </span>
          </div>
        </div>
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Model
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              12345677
            </span>
          </div>
        </div>
      </div>
      <div
        class="_filesSection_bffe77"
      >
        <div
          class="_documentContainer_fd77b0 _documentContainerDark_fd77b0 _documentContainerShort_fd77b0"
        >
          <div
            class="_documentPreviewIconContainerShort_fd77b0"
          >
            <div
              class="_documentPreviewIcon_fd77b0"
            >
              <div
                class="_icon_053e64"
              >
                <svg
                  color="#fff"
                  fill="none"
                  height="20"
                  stroke-width="0"
                  viewBox="0 0 24 24"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 10L20 20"
                    stroke="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div
              class="_documentContent_fd77b0"
            >
              <p
                class="body-s quasimoda _ellipsis_fd77b0 _documentName_fd77b0"
              >
                Fridge - freezer...
              </p>
              <p
                class="body-xss quasimoda _ellipsis_fd77b0 _documentDescription_fd77b0"
              >
                PDF
              </p>
            </div>
          </div>
        </div>
        <div
          class="_documentContainer_fd77b0 _documentContainerDark_fd77b0 _documentContainerShort_fd77b0"
        >
          <div
            class="_documentPreviewIconContainerShort_fd77b0"
          >
            <div
              class="_documentPreviewIcon_fd77b0"
            >
              <div
                class="_icon_053e64"
              >
                <svg
                  color="#fff"
                  fill="none"
                  height="20"
                  stroke-width="0"
                  viewBox="0 0 24 24"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 10L20 20"
                    stroke="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div
              class="_documentContent_fd77b0"
            >
              <p
                class="body-s quasimoda _ellipsis_fd77b0 _documentName_fd77b0"
              >
                User manual.pdf
              </p>
              <p
                class="body-xss quasimoda _ellipsis_fd77b0 _documentDescription_fd77b0"
              >
                PDF
              </p>
            </div>
          </div>
        </div>
        <div
          class="_documentContainer_fd77b0 _documentContainerDark_fd77b0 _documentContainerShort_fd77b0"
        >
          <div
            class="_documentPreviewIconContainerShort_fd77b0"
          >
            <div
              class="_documentPreviewIcon_fd77b0"
            >
              <div
                class="_icon_053e64"
              >
                <svg
                  color="#fff"
                  fill="none"
                  height="20"
                  stroke-width="0"
                  viewBox="0 0 24 24"
                  width="20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 10L20 20"
                    stroke="currentColor"
                  />
                </svg>
              </div>
            </div>
            <div
              class="_documentContent_fd77b0"
            >
              <p
                class="body-s quasimoda _ellipsis_fd77b0 _documentName_fd77b0"
              >
                Warranty card.pdf
              </p>
              <p
                class="body-xss quasimoda _ellipsis_fd77b0 _documentDescription_fd77b0"
              >
                PDF
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="_divider_bffe77"
    />
    <div
      class="_actions_bffe77"
    >
      <button
        data-testid="mock-button"
      >
        Edit details
      </button>
    </div>
  </div>
</div>
`;

exports[`PersonalizationCard Snapshots > matches snapshot with no files 1`] = `
<div>
  <div
    class="_container_bffe77  "
  >
    <div
      class="_content_bffe77"
    >
      <div
        class="_header_bffe77"
      >
        <h2
          class="_title_bffe77"
        >
          Fridge Freezer
        </h2>
      </div>
      <div
        class="_fieldsContainer_bffe77"
      >
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Brand
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              Bosch
            </span>
          </div>
        </div>
        <div
          class="_fieldRow_bffe77"
        >
          <div
            class="_fieldLabel_bffe77 "
          >
            Model
          </div>
          <div
            class="_fieldValue_bffe77"
          >
            <span
              class="_fieldValueContent_bffe77"
            >
              12345677
            </span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="_divider_bffe77"
    />
    <div
      class="_actions_bffe77"
    >
      <button
        data-testid="mock-button"
      >
        Edit details
      </button>
    </div>
  </div>
</div>
`;
