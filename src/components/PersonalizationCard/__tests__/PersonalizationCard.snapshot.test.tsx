import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { PersonalizationCard } from '../PersonalizationCard';
import { ButtonState } from '@/components/Button/Button.types';
import { PersonalizationCardFile } from '../PersonalizationCard.types';
import {
  createDefaultProps,
  createMultipleSnapshotMockFiles,
  createSnapshotMockFiles,
  mockFields,
} from './test-utils';

interface MockButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  state?: ButtonState;
  disabled?: boolean;
}

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
}));

vi.mock('ezheaders', () => ({
  headers: vi.fn(() => ({})),
}));

vi.mock('react-intersection-observer', () => ({
  useInView: vi.fn(() => ({
    ref: vi.fn(),
    inView: true,
  })),
}));

vi.mock('@/components/Button', () => ({
  Button: ({ children, onClick, state }: MockButtonProps) => (
    <button onClick={onClick} disabled={state === ButtonState.DISABLED} data-testid="mock-button">
      {children}
    </button>
  ),
  convertIconToPaths: vi.fn(() => [['path', { d: 'M10 10L20 20' }]]),
}));

vi.mock('@/components/FileUploadManager', () => ({
  FileUploadGrid: ({ files }: { files: PersonalizationCardFile[] }) => (
    <div data-testid="file-upload-grid">
      {files.map((file) => (
        <div key={file.id} data-testid="file-item">
          <span>{file.fileName}</span>
          {file.browserMimeType === 'application/pdf' ? <span>PDF</span> : <span>IMAGE</span>}
        </div>
      ))}
    </div>
  ),
}));

describe('PersonalizationCard Snapshots', () => {
  const mockFiles = createSnapshotMockFiles();
  const defaultProps = createDefaultProps({
    fields: mockFields,
    files: mockFiles,
  });

  it('matches default snapshot', () => {
    const { container } = render(<PersonalizationCard {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches editing mode snapshot', () => {
    const editingProps = createDefaultProps({
      ...defaultProps,
      isEditing: true,
    });
    const { container } = render(<PersonalizationCard {...editingProps} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with multiple files', () => {
    const multipleFiles = createMultipleSnapshotMockFiles();
    const propsWithMultipleFiles = createDefaultProps({
      ...defaultProps,
      files: multipleFiles,
    });

    const { container } = render(<PersonalizationCard {...propsWithMultipleFiles} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with no files', () => {
    const propsWithNoFiles = createDefaultProps({
      ...defaultProps,
      files: [],
    });
    const { container } = render(<PersonalizationCard {...propsWithNoFiles} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with custom className', () => {
    const propsWithClassName = createDefaultProps({
      ...defaultProps,
      className: 'custom-class',
    });
    const { container } = render(<PersonalizationCard {...propsWithClassName} />);
    expect(container).toMatchSnapshot();
  });
});
