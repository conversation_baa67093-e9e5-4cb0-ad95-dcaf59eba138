import { vi } from 'vitest';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
  PersonalizationCardProps,
} from '../PersonalizationCard.types';

export const mockFields: PersonalizationCardField[] = [
  {
    id: '1',
    label: 'Brand',
    value: 'Bosch',
    editable: true,
    type: 'text',
    placeholder: 'Enter brand',
  },
  {
    id: '2',
    label: 'Model',
    value: '12345677',
    editable: true,
    type: 'text',
    placeholder: 'Enter model',
  },
];

export const createMockFiles = (): PersonalizationCardFile[] => [
  {
    id: 1,
    fileName: 'document1.pdf',
    sizeInKiloBytes: 1024,
    browserMimeType: 'application/pdf',
    createdAt: new Date().toISOString(),
    uploadContext: 'chat',
    status: 'processingCompleted',
  },
  {
    id: 2,
    fileName: 'document2.pdf',
    sizeInKiloBytes: 2048,
    browserMimeType: 'application/pdf',
    createdAt: new Date().toISOString(),
    uploadContext: 'chat',
    status: 'processingCompleted',
  },
];

export const createSnapshotMockFiles = (): PersonalizationCardFile[] => [
  {
    id: 3,
    fileName: 'Fridge - freezer...',
    sizeInKiloBytes: 1024,
    browserMimeType: 'application/pdf',
    createdAt: new Date().toISOString(),
    uploadContext: 'chat',
    status: 'processingCompleted',
  },
];

export const createMultipleSnapshotMockFiles = (): PersonalizationCardFile[] => [
  ...createSnapshotMockFiles(),
  {
    id: 4,
    fileName: 'User manual.pdf',
    sizeInKiloBytes: 2048,
    browserMimeType: 'application/pdf',
    createdAt: new Date().toISOString(),
    uploadContext: 'chat',
    status: 'processingCompleted',
  },
  {
    id: 5,
    fileName: 'Warranty card.pdf',
    sizeInKiloBytes: 512,
    browserMimeType: 'application/pdf',
    createdAt: new Date().toISOString(),
    uploadContext: 'chat',
    status: 'processingCompleted',
  },
];

export const createDefaultProps = (
  overrides: Partial<PersonalizationCardProps> = {}
): PersonalizationCardProps => ({
  fields: mockFields,
  files: [],
  onSave: vi.fn(),
  onFileRemove: vi.fn(),
  onEdit: vi.fn(),
  onCancel: vi.fn(),
  isEditing: false,
  ...overrides,
});
