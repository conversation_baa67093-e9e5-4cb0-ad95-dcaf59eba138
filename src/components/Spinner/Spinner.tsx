import React from 'react';
import classNames from 'classnames';
import styles from './Spinner.module.scss';
import { SpinnerProps } from './Spinner.types';

export const Spinner: React.FC<SpinnerProps> = ({ 
  className,
  color = '#000',
  size = 24
}) => {
  return (
    <svg 
      data-testid="spinner"
      width={size}
      height={size}
      viewBox="0 0 32 32" 
      xmlns="http://www.w3.org/2000/svg"
      className={classNames(styles.spinner, className)}
      stroke={color}
    >
      <g className={styles.spinnerGroup}>
        <circle 
          cx="16" 
          cy="16" 
          r="14" 
          fill="none" 
          strokeWidth="2"
        />
      </g>
    </svg>
  );
}; 