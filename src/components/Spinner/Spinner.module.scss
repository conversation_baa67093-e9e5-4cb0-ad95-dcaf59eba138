.spinnerGroup {
  transform-origin: center;
  animation: spin 2s linear infinite;

  circle {
    stroke-linecap: round;
    animation: dash 1.5s ease-out infinite;
  }
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 0 150;
    stroke-dashoffset: 0;
  }
  47.5% {
    stroke-dasharray: 42 150;
    stroke-dashoffset: -16;
  }
  95%, 100% {
    stroke-dasharray: 42 150;
    stroke-dashoffset: -59;
  }
} 