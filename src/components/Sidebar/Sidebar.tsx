'use client';

import React, { MouseEvent, useEffect, useRef } from 'react';
import Link from 'next/link';
import classNames from 'classnames';
import { Add01Icon, IconSvgObject } from '@hugeicons/react';
import {
  Cancel01Icon,
  CheckListIcon,
  CustomerService01Icon,
  Home11Icon,
  House04Icon,
} from '@hugeicons-pro/core-stroke-standard';
import { SignInButton, SignUpButton, useAuth } from '@clerk/nextjs';
import { SidebarProps } from './Sidebar.types';
import styles from './Sidebar.module.scss';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { Logo } from './components/Logo/Logo';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { useChats } from '@/hooks/useChats';
import { usePathname, useRouter } from 'next/navigation';
import { NavigationItem } from './components/NavigationItem/NavigationItem';
import { useSidebar } from '@/hooks/useSidebar';
import { useBreakpoint } from '@/utils/breakpointUtils';
import useChatParams from '@/hooks/useChatParams';
import useBackendClient from '@/hooks/useBackendClient';

export const Sidebar: React.FC<SidebarProps> = ({}) => {
  const { isSignedIn } = useAuth();
  const { chats, hasMore, loadMoreChats } = useChats();
  const sidebarRef = useRef<HTMLDivElement>(null);
  const recentChatsContainerRef = useRef<HTMLDivElement>(null);
  const sentinelRef = useRef(null);
  const { chatId: activeChatId } = useChatParams();
  const pathname = usePathname();
  const { client } = useBackendClient();

  const router = useRouter();

  const { closeSidebar, toggleSidebar } = useSidebar();
  const { isMobile } = useBreakpoint();
  const { isStreamingMessage } = useChats();

  useEffect(() => {
    const sentinel = sentinelRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(async (entry) => {
          if (entry.isIntersecting && hasMore) {
            loadMoreChats(client);
          }
        });
      },
      {
        root: recentChatsContainerRef.current,
        threshold: 0.1,
      }
    );

    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
    };
  }, [hasMore, loadMoreChats, client]);

  const chatsWithId = Object.values(chats).filter((chat) => 'id' in chat);

  const handleCreateNewChat = async (e: MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();
    if (isMobile) {
      closeSidebar();
    }
    router?.push(`/`);
  };

  const handleNavigationClick = (url: string) => (e: MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();
    if (isMobile) {
      closeSidebar();
    }
    router?.push(url);
  };

  const navigationItems = [
    {
      icon: <HugeiconsIcon icon={Home11Icon as unknown as IconSvgObject} size={20} />,
      label: 'Home',
      url: '/',
      isComingSoon: false,
      guestAccessible: true,
      onClick: handleCreateNewChat,
    },
    {
      icon: <HugeiconsIcon icon={CheckListIcon as unknown as IconSvgObject} size={20} />,
      label: 'To-Do List',
      url: '/todo-list',
      isComingSoon: false,
      guestAccessible: true,
      onClick: handleNavigationClick('/todo-list'),
    },
    {
      icon: <HugeiconsIcon icon={House04Icon as unknown as IconSvgObject} size={20} />,
      label: 'Property Profile',
      url: '/property-profile',
      guestAccessible: true,
      onClick: handleNavigationClick('/property-profile'),
    },
  ];

  return (
    <aside className={styles.sidebar} ref={sidebarRef}>
      <div className={styles.container}>
        <div
          className={classNames(styles.header, {
            [styles.mobile]: isMobile,
          })}
        >
          <div className={styles.logoContainer}>
            {isMobile && (
              <div className={styles.burger} onClick={toggleSidebar}>
                <HugeiconsIcon
                  icon={Cancel01Icon as unknown as IconSvgObject}
                  size={24}
                  color="#6b7280"
                />
              </div>
            )}
            <div
              className={classNames(styles.logo, {
                [styles.mobile]: isMobile,
              })}
            >
              <Logo />
            </div>
          </div>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            state={isStreamingMessage ? ButtonState.DISABLED : ButtonState.DEFAULT}
            size={ButtonSize.L}
            onClick={() => {
              if (isStreamingMessage) return;
              if (isMobile) {
                closeSidebar();
              }
              router?.push(`/`);
            }}
            showLeftIcon
            leftIcon={Add01Icon}
          >
            Create new
          </Button>
        </div>

        <div className={styles.content}>
          <nav>
            <ul className={classNames('space-y-2', styles.navContainer)}>
              {navigationItems.map((item) => (
                <li
                  key={item.url}
                  className={classNames({
                    [styles.comingSoon]: item.isComingSoon,
                  })}
                >
                  <NavigationItem
                    icon={item.icon}
                    label={item.label}
                    isAuthenticated={isSignedIn ?? false}
                    isComingSoon={item.isComingSoon}
                    onClick={item.onClick}
                    url={item.url}
                    guestAccessible={item.guestAccessible}
                    className={classNames({
                      [styles.navItem]: true,
                      [styles.active]:
                        !item.isComingSoon &&
                        pathname &&
                        (item.url === '/' ? pathname === '/' : pathname.startsWith(item.url)),
                    })}
                  />
                </li>
              ))}
              <li>
                <NavigationItem
                  icon={
                    <HugeiconsIcon
                      icon={CustomerService01Icon as unknown as IconSvgObject}
                      size={20}
                    />
                  }
                  label="Help"
                  isAuthenticated={isSignedIn ?? false}
                  url="https://help.heyalfie.com/help-centre"
                  guestAccessible={true}
                  isExternal={true}
                  className={styles.navItem}
                />
              </li>
            </ul>
          </nav>

          {/* Show recent chats for both authenticated and guest users */}
          <div className={styles.recentChats}>
            <h2 className={styles.recentChatsTitle}>RECENT CHATS</h2>
            <div className={styles.recentChatsWrapper}>
              <div className={styles.recentChatsContainer} ref={recentChatsContainerRef}>
                <ul className="space-y-1">
                  {chatsWithId.map((chat) => (
                    <li key={chat.id}>
                      <Link
                        href={`/chats/${chat.id}`}
                        onClick={(e) => {
                          e.preventDefault();

                          if (isMobile) {
                            closeSidebar();
                          }

                          router.push(`/chats/${chat.id}`);
                        }}
                        className={classNames(styles.chatItem, {
                          [styles.active]: chat.id === activeChatId,
                        })}
                      >
                        <span className={styles.chatTitle}>{chat.title}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
                {hasMore && <div ref={sentinelRef} style={{ height: 20 }} />}
              </div>
            </div>
          </div>

          {!isSignedIn && (
            <div className={styles.authContainer}>
              <p className={styles.authText}>Sign up or log in to access all the features</p>
              <div className="flex gap-4">
                <SignUpButton mode="modal">
                  <Button
                    type={ButtonType.PRIMARY}
                    color={ButtonColor.GREEN_PRIMARY}
                    size={ButtonSize.BASE}
                    className="flex-1"
                  >
                    Sign up
                  </Button>
                </SignUpButton>
                <SignInButton mode="modal">
                  <Button
                    type={ButtonType.SECONDARY}
                    color={ButtonColor.GREEN_PRIMARY}
                    size={ButtonSize.BASE}
                    className="flex-1"
                  >
                    Log in
                  </Button>
                </SignInButton>
              </div>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
};
