import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { vi } from 'vitest';
import { Sidebar } from '../Sidebar';

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

const IntersectionObserverMock = vi.fn(() => ({
  disconnect: vi.fn(),
  observe: vi.fn(),
  takeRecords: vi.fn(),
  unobserve: vi.fn(),
}));

vi.stubGlobal('IntersectionObserver', IntersectionObserverMock);

vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    getToken: () => Promise.resolve('mock-token'),
    isAuthenticated: true,
  }),
  SignInButton: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  SignUpButton: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

vi.mock('@/hooks/useChats', () => ({
  useChats: () => ({
    chats: [
      { id: 1, title: 'Test chat 1', status: 'active' },
      { id: 2, title: 'Test chat 2', status: 'active' },
    ],
    isLoading: false,
    hasMore: true,
    fetchChats: vi.fn(),
    loadMoreChats: vi.fn(),
  }),
}));

describe('Sidebar Snapshots', () => {
  it('matches authenticated snapshot', () => {
    const { container } = render(<Sidebar isAuthenticated={true} />);
    expect(container).toMatchSnapshot();
  });

  it('matches unauthenticated snapshot', () => {
    const { container } = render(<Sidebar isAuthenticated={false} />);
    expect(container).toMatchSnapshot();
  });

  it('matches snapshot with settings open', () => {
    const { container } = render(
      <Sidebar userName="Alfie" isAuthenticated={true} onCreateNew={() => { }} />
    );
    const settingsButton = container.querySelector('button');
    if (settingsButton) {
      fireEvent.click(settingsButton);
    }
    expect(container).toMatchSnapshot();
  });
});
