.demoWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  flex-grow: 1;
}

.title {
  font-family: '<PERSON>yke<PERSON>', <PERSON><PERSON>, sans-serif;
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 8px;
  margin-top: 36px;
  color: #2d2926;
}

.description {
  color: #6b6865;
  font-size: 15px;
  margin-bottom: 16px;
}

.formGroup {
  margin-bottom: 12px;
  display: flex;
  flex-direction: column;
}

.label {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 7px;
  color: #23201d;
}

.input,
.select {
  width: 100%;
  padding: 8px 12px;
  border: 1.5px solid #ebe5e5;
  border-radius: 8px;
  font-size: 16px;
  color: #23201d;
  outline: none;
  transition: border-color 0.2s;
  min-height: 48px;
}

textarea.input {
  resize: none;
  min-height: 70px;
}

.inputArrow {
  right: 8px;
  top: 60%;
  position: absolute;
  color: var(--colors-gray-500);
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  z-index: 10;
  margin-top: 4px;
  border: 1px solid #ebe5e5;
}

.option {
  padding: 8px 8px 8px 12px;
  font-size: 16px;
  color: #23201d;
  cursor: pointer;
  transition: background-color 0.2s;
}

.option:last-child {
  border-bottom: none;
}

.option:hover {
  background-color: #f3f7f5;
}

.buttonContainer {
  margin-top: auto;
  display: flex;
  justify-content: center;
  width: 100%;

  @media (max-width: 768px) {
    position: sticky;
    bottom: 0;
  }
}

.demoButton {
  width: 100%;
}

.successWrapper {
  text-align: center;
  margin-top: 107px;
}

.successIcon {
  color: black;
  margin-bottom: 24px;
}