.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0;
  padding: 0;
}

.title {
  font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  margin-bottom: 12px;
  color: var(--colors-gray-900);
}

.subtitle {
  font-family: 'Quasimoda', Helvetica;
  font-size: 16px;
  font-weight: bold;
  line-height: 150%;
  margin-bottom: 4px;
  color: var(--colors-gray-900);
  display: block;

  &:not(:has(+ .helperText)) {
    margin-bottom: 12px;
  }
}

.helperText {
  font-family: 'Quasimoda', Helvetica;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 13px;
  color: var(--colors-gray-500);
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: 0;
  margin-top: 8px;
}

.radioOption {
  border: 1px solid var(--colors-gray-200);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: border-color 0.2s ease;
  margin-bottom: 8px;

  &:hover {
    border-color: var(--colors-gray-300);
  }

  &.selected {
    border: 1px solid #1b6e5a;
  }
}

.radioOption :global([class*='content']) {
  align-items: center;
}

.inputContainer {
  margin-bottom: 24px;
}

.propertyTypeTitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--colors-gray-900);
}

.propertyTypeContainer {
  margin-bottom: 16px;
}

.completionContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  text-align: center;
}

.completionImage {
  width: 230px;
  height: 230px;
  margin-bottom: 32px;
  -webkit-font-smoothing: antialiased;
  image-rendering: -webkit-optimize-contrast;
}

.completionTitle {
  font-weight: 700;
  font-size: 28px;
  color: #2d2926;
  margin-bottom: 16px;

  @media (max-width: 768px) {
    font-size: 20px;
    margin-bottom: 8px;
  }
}

.completionDescription {
  color: #6b6865;
  font-size: 18px;
  margin-bottom: 40px;
  max-width: 420px;

  @media (max-width: 768px) {
    font-size: 15px;
    margin-bottom: 16px;
  }
}

.buttonGroup {
  width: 100%;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  @media (max-width: 768px) {
    gap: 16px;
  }
}

.container :global(.AddressInput-module__container--Qh7cm) {
  padding: 0;
}

.container :global(.AddressInput-module__title--JdR5x) {
  display: none;
}

.inputContainer :global([class*='AddressInput_container']) {
  padding: 0 !important;
}