import React, { useState } from 'react';
import styles from './OnboardingSteps.module.scss';
import { Radio } from '@/components/Radio/Radio';
import { RadioState } from '@/components/Radio/Radio.types';
import { AddressInput } from '@/components/AddressInput/AddressInput';
import { AddressType } from '@/components/AddressInput/AddressInput.types';
import { Button } from '@/components/Button/Button';
import { ButtonColor, ButtonSize, ButtonType } from '@/components/Button/Button.types';
import classNames from 'classnames';
import { PropertyTenureType, PropertyType, UserPropertyRelationType } from '@/api/properties';

export interface OnboardingStepsProps {
  onRoleSelected?: (role: UserPropertyRelationType) => void;
  onAddressSelected?: (address: AddressType | undefined) => void;
  step?: number;
  selectedPropertyType?: PropertyType | null;
  selectedOwnershipType?: PropertyTenureType | null;
  onPropertyTypeSelected?: (type: PropertyType) => void;
  onOwnershipTypeSelected?: (type: PropertyTenureType) => void;
  selectedRole?: UserPropertyRelationType | null;
  forceDropdownPosition?: 'top' | 'bottom';
}

type Item<T> = {
  label: string;
  value: T;
};

export interface UserRoleOption extends Item<UserPropertyRelationType> {
  description: string;
}

const userRoleOptions: UserRoleOption[] = [
  {
    value: UserPropertyRelationType.OwnerAndOccupier,
    label: 'Owner and occupier',
    description: 'You own the property and also occupy or live in it',
  },
  {
    value: UserPropertyRelationType.Landlord,
    label: 'Landlord',
    description: 'You own the property and tenant it out for rent',
  },
  {
    value: UserPropertyRelationType.Tenant,
    label: 'Tenant',
    description: 'You rent this property from a landlord',
  },
  {
    value: UserPropertyRelationType.ManagingProfessional,
    label: 'Property management professional',
    description: 'You work at a lettings or property management company',
  },
];

const propertyTypes: Item<PropertyType>[] = [
  { label: 'House', value: PropertyType.House },
  { label: 'Flat', value: PropertyType.Flat },
  { label: 'Office', value: PropertyType.Office },
  { label: 'Retail', value: PropertyType.Retail },
];

const ownershipTypes: Item<PropertyTenureType>[] = [
  { label: 'Freehold', value: PropertyTenureType.Freehold },
  { label: 'Leasehold', value: PropertyTenureType.Leasehold },
  { label: 'Share of freehold', value: PropertyTenureType.ShareOfFreehold },
];

export const OnboardingSteps: React.FC<OnboardingStepsProps> = ({
  onRoleSelected,
  onAddressSelected,
  step = 0,
  selectedPropertyType,
  selectedOwnershipType,
  onPropertyTypeSelected,
  onOwnershipTypeSelected,
  selectedRole,
  forceDropdownPosition,
}) => {
  const [savedRedirectUrl, setSavedRedirectUrl] = useState<string | null>(null);

  const getRelativePathFromSavedUrl = (savedUrl: string): string => {
    try {
      if (savedUrl.startsWith('http://') || savedUrl.startsWith('https://')) {
        const url = new URL(savedUrl);
        return url.pathname + url.search;
      } else {
        return savedUrl;
      }
    } catch (error) {
      console.error('Error parsing saved redirect URL:', error);
      return '/';
    }
  };

  const handleRoleChange = (role: UserPropertyRelationType) => {
    if (onRoleSelected) {
      onRoleSelected(role);
    }
  };

  if (step === 1) {
    let subtitle = '';
    switch (selectedRole) {
      case UserPropertyRelationType.OwnerAndOccupier:
        subtitle = 'Find your address';
        break;
      case UserPropertyRelationType.Landlord:
        subtitle = 'Find the property that you rent out';
        break;
      case UserPropertyRelationType.Tenant:
        subtitle = 'Find the property that you rent and live in';
        break;
      case UserPropertyRelationType.ManagingProfessional:
        subtitle = 'Find the property that you manage';
        break;
      default:
        subtitle = '';
    }

    return (
      <div className={styles.container}>
        <h2 className={styles.title}>
          {selectedRole === 'landlord' ? 'Property address' : 'Your address'}
        </h2>
        <p className={styles.subtitle}>{subtitle}</p>
        <div className={styles.inputContainer}>
          <AddressInput
            title=""
            onChange={onAddressSelected}
            placeholder="Find address by postcode"
            manualEntryText="Enter address manually"
            hideBorder
            forceDropdownPosition={forceDropdownPosition}
            hideTitle
          />
        </div>
      </div>
    );
  }

  if (step === 2) {
    return (
      <div className={styles.container}>
        <h2 className={styles.title}>Property type</h2>
        <div className={styles.subtitle}>What type of property is this?</div>
        <div className={styles.propertyTypeContainer}>
          {propertyTypes.map((type) => (
            <div
              key={type.value}
              className={classNames(styles.radioOption, {
                [styles.selected]: selectedPropertyType === type.value,
              })}
            >
              <Radio
                labelText={type.label}
                checked={selectedPropertyType === type.value}
                onChange={() => onPropertyTypeSelected && onPropertyTypeSelected(type.value)}
                state={RadioState.NORMAL}
                name="propertyType"
                value={type.value}
              />
            </div>
          ))}
        </div>

        {selectedRole !== UserPropertyRelationType.Tenant &&
          selectedRole !== UserPropertyRelationType.ManagingProfessional && (
            <>
              <div className={styles.subtitle}>What type of ownership do you have?</div>
              <div>
                {ownershipTypes.map((type) => (
                  <div
                    key={type.value}
                    className={classNames(styles.radioOption, {
                      [styles.selected]: selectedOwnershipType === type.value,
                    })}
                  >
                    <Radio
                      labelText={type.label}
                      checked={selectedOwnershipType === type.value}
                      onChange={() =>
                        onOwnershipTypeSelected && onOwnershipTypeSelected(type.value)
                      }
                      state={RadioState.NORMAL}
                      name="ownershipType"
                      value={type.value}
                    />
                  </div>
                ))}
              </div>
            </>
          )}
      </div>
    );
  }

  if (step === 3) {
    return (
      <div className={`${styles.container} ${styles.completionContainer}`}>
        <img
          src="/House_8x.png"
          alt="House"
          className={styles.completionImage}
          srcSet="/House_8x.png 8x"
        />
        <h2 className={styles.completionTitle}>Account set up complete</h2>
        <div className={styles.completionDescription}>
          Tell us more or upload documents to build your Property Profile in order to unlock even
          more personalised assistance from Hey Alfie
        </div>
        <div className={styles.buttonGroup}>
          {savedRedirectUrl ? (
            <>
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.GREEN_PRIMARY}
                size={ButtonSize.L}
                className={styles.button}
                onClick={() => {}}
                href={getRelativePathFromSavedUrl(savedRedirectUrl)}
              >
                Continue to chat
              </Button>
              <Button
                type={ButtonType.SECONDARY}
                color={ButtonColor.GREEN_PRIMARY}
                size={ButtonSize.L}
                className={styles.button}
                href="/property-profile"
              >
                Build my Property Profile
              </Button>
            </>
          ) : (
            <>
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.GREEN_PRIMARY}
                size={ButtonSize.L}
                className={styles.button}
                onClick={() => {}}
                href="/property-profile"
              >
                Build my Property Profile
              </Button>
              <Button
                type={ButtonType.SECONDARY}
                color={ButtonColor.GREEN_PRIMARY}
                size={ButtonSize.L}
                className={styles.button}
                href="/"
              >
                Go to my account
              </Button>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <h2 className={styles.title}>How will you use Hey Alfie?</h2>
      <p className={styles.subtitle}>Select the option that describes your situation</p>
      <p className={styles.helperText}>
        If you have multiple properties, select the option that best describes your main usage of
        Hey Alfie
      </p>

      <div className={styles.optionsContainer}>
        {userRoleOptions.map((option) => (
          <div
            key={option.value}
            className={classNames(styles.radioOption, {
              [styles.selected]: selectedRole === option.value,
            })}
          >
            <Radio
              labelText={option.label}
              helperText={option.description}
              checked={selectedRole === option.value}
              onChange={() => handleRoleChange(option.value)}
              state={RadioState.NORMAL}
              name="userRole"
              value={option.value}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
