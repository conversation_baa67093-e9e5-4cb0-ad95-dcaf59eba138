import type { Meta, StoryObj } from '@storybook/react';
import { PropertyForm } from './PropertyForm';

const meta: Meta<typeof PropertyForm> = {
  title: 'Components/PropertyForm',
  component: PropertyForm,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'Property information form in Onboarding style. Contains fields for selecting ownership type, property type, property sub type, number of rooms and other characteristics.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onSubmit: { action: 'submitted' },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
    initialData: {
      control: 'object',
      description: 'Initial form data',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSubmit: (data: any) => {
      console.log('Form submitted:', data);
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'Basic form with all fields in empty state. User can fill each field individually.',
      },
    },
  },
};
