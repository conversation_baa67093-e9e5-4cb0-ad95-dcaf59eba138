import React from 'react';
import classNames from 'classnames';
import { Input } from '../Input';
import { Button } from '../Button';
import { FormDropdown } from '../FormDropdown';
import { FormRadioCard } from '../FormRadioCard';
import { ButtonType } from '../Button/Button.types';
import { InputSize } from '../Input/Input.types';
import { ButtonState } from '../Button/Button.types';
import { PropertyFormProps, PropertyFormData, YesNoOption } from './PropertyForm.types';
import { usePropertyForm } from '../../hooks/usePropertyForm';
import {
  DROPDOWN_CONFIGS,
  INPUT_CONFIGS,
  YES_NO_CONFIGS,
  DropdownConfig,
} from './PropertyForm.constants';
import styles from './PropertyForm.module.scss';

export const PropertyForm: React.FC<PropertyFormProps> = ({
  initialData = {},
  onSubmit,
  value,
  onChange,
  className,
  showUploadButton = true,
}) => {
  const isControlled = value !== undefined && onChange !== undefined;

  const internalForm = usePropertyForm(initialData);
  const formData = isControlled ? value : internalForm.formData;
  const isFormComplete = isControlled
    ? () => Object.values(formData).some((val) => String(val).trim())
    : internalForm.isFormComplete;

  const handleInputChange = (field: keyof PropertyFormData, inputValue: string) => {
    if (isControlled) {
      onChange!({
        ...formData,
        [field]: inputValue,
      });
    } else {
      internalForm.handleInputChange(field, inputValue);
    }
  };

  const handleRadioChange = (field: keyof PropertyFormData, radioValue: YesNoOption) => {
    if (isControlled) {
      onChange!({
        ...formData,
        [field]: radioValue,
      });
    } else {
      internalForm.handleRadioChange(field, radioValue);
    }
  };

  const handleDropdownSelect = (field: keyof PropertyFormData, selectedValue: string) => {
    const updatedFormData = {
      ...formData,
      [field]: selectedValue,
    };

    if (isControlled) {
      onChange!(updatedFormData);
    } else {
      internalForm.handleDropdownSelect(field, selectedValue);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit?.(formData);
  };

  const renderDropdown = (config: DropdownConfig) => {
    const value = formData[config.field] as string;

    return (
      <div className={styles.section} key={config.field}>
        <div className={styles.subtitle}>{config.title}</div>
        <FormDropdown
          value={value}
          onChange={(selectedValue) => handleDropdownSelect(config.field, selectedValue)}
          options={config.options}
          placeholder={config.placeholder}
        />
      </div>
    );
  };

  const renderYesNoRadioGroup = (title: string, field: keyof PropertyFormData) => {
    const value = formData[field] as YesNoOption | '';

    return (
      <div className={styles.section} key={field}>
        <div className={styles.subtitle}>{title}</div>
        <div className={styles.yesNoContainer}>
          <FormRadioCard
            label="Yes"
            checked={value === YesNoOption.YES}
            onChange={() => handleRadioChange(field, YesNoOption.YES)}
            name={field}
            value={YesNoOption.YES}
          />
          <FormRadioCard
            label="No"
            checked={value === YesNoOption.NO}
            onChange={() => handleRadioChange(field, YesNoOption.NO)}
            name={field}
            value={YesNoOption.NO}
          />
        </div>
      </div>
    );
  };

  const renderInputSection = (
    title: string,
    field: keyof PropertyFormData,
    placeholder: string,
    inputType: 'text' | 'number' | 'email' | 'password' | 'tel' | 'url' = 'text'
  ) => (
    <div className={styles.section} key={field}>
      <div className={styles.subtitle}>{title}</div>
      <Input
        labelText=""
        placeholderText={placeholder}
        value={formData[field] as string}
        onChange={(value) => handleInputChange(field, value)}
        size={InputSize.REGULAR}
        showLabel={false}
        showLeftIcon={false}
        showHelperText={false}
        fullWidth
        greenBorder
        disableClear
        inputType={inputType}
      />
    </div>
  );

  const formContent = (
    <>
      {DROPDOWN_CONFIGS.map((config) => renderDropdown(config))}

      {INPUT_CONFIGS.map((config) =>
        renderInputSection(config.title, config.field, config.placeholder, config.inputType)
      )}

      {YES_NO_CONFIGS.map((config) => renderYesNoRadioGroup(config.title, config.field))}

      {showUploadButton && (
        <div className={styles.uploadContainer}>
          <Button
            type={ButtonType.PRIMARY}
            className={styles.uploadButton}
            state={!isFormComplete() ? ButtonState.DISABLED : ButtonState.DEFAULT}
          >
            Upload documents
          </Button>
        </div>
      )}
    </>
  );

  return (
    <div className={classNames(styles.container, className)}>
      {onSubmit ? (
        <form onSubmit={handleSubmit} className={styles.form}>
          {formContent}
        </form>
      ) : (
        <div className={styles.form}>{formContent}</div>
      )}
    </div>
  );
};
