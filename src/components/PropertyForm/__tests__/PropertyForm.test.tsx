import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { PropertyForm } from '../PropertyForm';
import {
  OwnershipType,
  PropertyType,
  PropertySubType,
  YesNoOption,
  PropertyFormData,
  UserRole,
} from '../PropertyForm.types';

// Mock HugeIcons
vi.mock('@hugeicons-pro/core-stroke-rounded', () => ({
  ArrowDown01Icon: ({ className, size }: { className?: string; size?: number }) => (
    <div data-testid="arrow-icon" className={className}>
      Arrow {size}
    </div>
  ),
}));

vi.mock('../../HugeiconsIcon', () => ({
  HugeiconsIcon: ({ size, className }: { size?: number; className?: string }) => (
    <div data-testid="hugicons-icon" className={className}>
      Mock Icon {size}
    </div>
  ),
}));

describe('PropertyForm', () => {
  const mockOnSubmit = vi.fn();

  const defaultProps = {
    onSubmit: mockOnSubmit,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  // Rendering Tests
  describe('Rendering', () => {
    it('renders base form fields correctly', () => {
      render(<PropertyForm {...defaultProps} />);

      // Form sections should be visible
      expect(screen.getByText('Relationship')).toBeInTheDocument();
      expect(screen.getByText('Ownership type')).toBeInTheDocument();
      expect(screen.getByText('Property type')).toBeInTheDocument();
      expect(screen.getByText('Property sub type')).toBeInTheDocument(); // Always shown now
      expect(screen.getByText('Number of bedrooms')).toBeInTheDocument();
      expect(screen.getByText('Number of bathrooms')).toBeInTheDocument();
      expect(screen.getByText('Number of floors')).toBeInTheDocument(); // Always shown now
      expect(screen.getByText('Floor property is on')).toBeInTheDocument(); // Always shown now
      expect(screen.getByText('Gross internal area (ft²)')).toBeInTheDocument();
      expect(screen.getByText('Balcony/Terrace')).toBeInTheDocument();
      expect(screen.getByText('Garden')).toBeInTheDocument();
      expect(screen.getByText('Swimming Pool')).toBeInTheDocument();
      expect(screen.getByText('Upload documents')).toBeInTheDocument();
    });

    it('renders house-specific fields when property type is House', () => {
      const initialData: Partial<PropertyFormData> = {
        propertyType: PropertyType.HOUSE,
      };

      render(<PropertyForm {...defaultProps} initialData={initialData} />);

      // All fields should be visible regardless of property type
      expect(screen.getByText('Property sub type')).toBeInTheDocument();
      expect(screen.getByText('Number of floors')).toBeInTheDocument();
      expect(screen.getByText('Floor property is on')).toBeInTheDocument(); // Always shown now
    });

    it('renders floor field when property type is not House', () => {
      const initialData: Partial<PropertyFormData> = {
        propertyType: PropertyType.FLAT,
      };

      render(<PropertyForm {...defaultProps} initialData={initialData} />);

      // All fields should be visible regardless of property type
      expect(screen.getByText('Property sub type')).toBeInTheDocument(); // Always shown now
      expect(screen.getByText('Number of floors')).toBeInTheDocument(); // Always shown now
      expect(screen.getByText('Floor property is on')).toBeInTheDocument();
    });

    it('renders with initial data correctly', () => {
      const initialData: Partial<PropertyFormData> = {
        ownershipType: OwnershipType.FREEHOLD,
        propertyType: PropertyType.HOUSE,
        propertySubType: PropertySubType.DETACHED,
        numberOfBedrooms: '3',
        numberOfBathrooms: '2',
        balconyTerrace: YesNoOption.YES,
        garden: YesNoOption.NO,
      };

      render(<PropertyForm {...defaultProps} initialData={initialData} />);

      expect(screen.getByText(OwnershipType.FREEHOLD)).toBeInTheDocument();
      expect(screen.getByText(PropertyType.HOUSE)).toBeInTheDocument();
      expect(screen.getByText(PropertySubType.DETACHED)).toBeInTheDocument();

      expect(screen.getByDisplayValue('3')).toBeInTheDocument();
      expect(screen.getByDisplayValue('2')).toBeInTheDocument();

      const yesRadios = screen.getAllByLabelText('Yes');
      const noRadios = screen.getAllByLabelText('No');
      expect(yesRadios[0]).toBeChecked(); // balconyTerrace
      expect(noRadios[1]).toBeChecked(); // garden
    });

    it('applies custom className correctly', () => {
      const customClass = 'custom-form-class';
      const { container } = render(<PropertyForm {...defaultProps} className={customClass} />);
      expect(container.firstChild).toHaveClass(customClass);
    });
  });

  // Dropdown Tests
  describe('Dropdowns', () => {
    it('opens and closes ownership type dropdown correctly', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      // Find the ownership type dropdown button (second one after userRole)
      const dropdownButtons = screen.getAllByText('Choose an option');
      const ownershipDropdown = dropdownButtons[1]; // Changed from 0 to 1
      await user.click(ownershipDropdown);

      // Check dropdown options appear
      await waitFor(() => {
        expect(screen.getByText(OwnershipType.FREEHOLD)).toBeInTheDocument();
        expect(screen.getByText(OwnershipType.LEASEHOLD)).toBeInTheDocument();
        expect(screen.getByText(OwnershipType.SHARE_OF_FREEHOLD)).toBeInTheDocument();
      });

      // Select an option
      await user.click(screen.getByText(OwnershipType.FREEHOLD));

      // Check dropdown closes and value is selected (now in button text)
      await waitFor(() => {
        expect(screen.getByText(OwnershipType.FREEHOLD)).toBeInTheDocument();
        expect(screen.queryByText(OwnershipType.LEASEHOLD)).not.toBeInTheDocument();
      });
    });

    it('opens and closes property type dropdown correctly', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      // Find the property type dropdown button (third "Choose an option")
      const dropdownButtons = screen.getAllByText('Choose an option');
      const propertyTypeDropdown = dropdownButtons[2]; // Changed from 1 to 2
      await user.click(propertyTypeDropdown);

      await waitFor(() => {
        expect(screen.getByText(PropertyType.HOUSE)).toBeInTheDocument();
        expect(screen.getByText(PropertyType.FLAT)).toBeInTheDocument();
        expect(screen.getByText(PropertyType.OFFICE)).toBeInTheDocument();
        expect(screen.getByText(PropertyType.RETAIL)).toBeInTheDocument();
      });

      await user.click(screen.getByText(PropertyType.FLAT));

      await waitFor(() => {
        expect(screen.getByText(PropertyType.FLAT)).toBeInTheDocument();
      });
    });

    it('opens and closes property sub type dropdown correctly', async () => {
      const user = userEvent.setup();

      // First set property type to House to show property sub type dropdown
      const initialData: Partial<PropertyFormData> = {
        propertyType: PropertyType.HOUSE,
      };

      render(<PropertyForm {...defaultProps} initialData={initialData} />);

      // Find all dropdown buttons and select the property sub type one (last one)
      const dropdownButtons = screen.getAllByRole('button').filter(button =>
        button.textContent?.includes('Choose an option')
      );
      const propertySubTypeDropdown = dropdownButtons[2]; // Third dropdown with "Choose an option"

      await user.click(propertySubTypeDropdown);

      await waitFor(() => {
        expect(screen.getByText(PropertySubType.TERRACED)).toBeInTheDocument();
        expect(screen.getByText(PropertySubType.SEMI_DETACHED)).toBeInTheDocument();
        expect(screen.getByText(PropertySubType.DETACHED)).toBeInTheDocument();
      });

      await user.click(screen.getByText(PropertySubType.DETACHED));

      await waitFor(() => {
        expect(screen.getByText(PropertySubType.DETACHED)).toBeInTheDocument();
      });
    });

    it('closes dropdown when clicking outside', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      // Find the ownership type dropdown button (second one after userRole)
      const dropdownButtons = screen.getAllByText('Choose an option');
      const ownershipDropdown = dropdownButtons[1]; // Changed from [0] to [1] for ownership type
      await user.click(ownershipDropdown);

      await waitFor(() => {
        expect(screen.getByText(OwnershipType.FREEHOLD)).toBeInTheDocument();
      });

      // Click outside
      await user.click(document.body);

      await waitFor(() => {
        expect(screen.queryByText(OwnershipType.FREEHOLD)).not.toBeInTheDocument();
      });
    });
  });

  // Input Tests
  describe('Input Fields', () => {
    it('updates input values when typing', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      const numberInputs = screen.getAllByPlaceholderText('e.g. 2');
      const bedroomsInput = numberInputs[0]; // First input with this placeholder is bedrooms
      await user.type(bedroomsInput, '4');

      // Check input value updates
      expect(bedroomsInput).toHaveValue(4);
    });

    it('displays correct placeholders for base inputs', () => {
      render(<PropertyForm {...defaultProps} />);

      const placeholderInputs = screen.getAllByPlaceholderText('e.g. 2');
      expect(placeholderInputs).toHaveLength(4); // bedrooms, bathrooms, floors, floor property is on

      expect(screen.getByPlaceholderText('e.g. 800')).toBeInTheDocument(); // area
    });

    it('displays correct placeholders for house inputs', () => {
      const initialData: Partial<PropertyFormData> = {
        propertyType: PropertyType.HOUSE,
      };

      render(<PropertyForm {...defaultProps} initialData={initialData} />);

      const placeholderInputs = screen.getAllByPlaceholderText('e.g. 2');
      expect(placeholderInputs).toHaveLength(4); // bedrooms, bathrooms, floors, floor property is on - all always shown
    });

    it('displays correct placeholders for dropdowns by default', () => {
      render(<PropertyForm {...defaultProps} />);

      const dropdownPlaceholders = screen.getAllByText('Choose an option');
      expect(dropdownPlaceholders).toHaveLength(4); // userRole, ownership, property type, property sub type - all always shown
    });

    it('displays correct placeholders for dropdowns when house selected', () => {
      const initialData: Partial<PropertyFormData> = {
        propertyType: PropertyType.HOUSE,
      };

      render(<PropertyForm {...defaultProps} initialData={initialData} />);

      // All dropdowns always show
      const dropdownPlaceholders = screen.getAllByText('Choose an option');
      expect(dropdownPlaceholders).toHaveLength(3); // userRole, ownership type, property sub type (property type shows "House")
    });
  });

  // Radio Button Tests
  describe('Yes/No Radio Groups', () => {
    it('handles balcony/terrace radio selection correctly', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      const yesRadios = screen.getAllByLabelText('Yes');
      const balconyYesRadio = yesRadios[0]; // First Yes radio is for balcony/terrace

      await user.click(balconyYesRadio);
      expect(balconyYesRadio).toBeChecked();

      const noRadios = screen.getAllByLabelText('No');
      const balconyNoRadio = noRadios[0]; // First No radio is for balcony/terrace

      await user.click(balconyNoRadio);
      expect(balconyNoRadio).toBeChecked();
      expect(balconyYesRadio).not.toBeChecked();
    });

    it('handles garden radio selection correctly', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      const yesRadios = screen.getAllByLabelText('Yes');
      const gardenYesRadio = yesRadios[1]; // Second Yes radio is for garden

      await user.click(gardenYesRadio);
      expect(gardenYesRadio).toBeChecked();
    });

    it('handles swimming pool radio selection correctly', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      const yesRadios = screen.getAllByLabelText('Yes');
      const poolYesRadio = yesRadios[2]; // Third Yes radio is for swimming pool

      await user.click(poolYesRadio);
      expect(poolYesRadio).toBeChecked();
    });
  });

  // Form Submission Tests
  describe('Form Submission', () => {
    it('calls onSubmit with form data when submitted', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      // Fill some form data
      const numberInputs = screen.getAllByPlaceholderText('e.g. 2');
      const bedroomsInput = numberInputs[0]; // First input is bedrooms
      await user.type(bedroomsInput, '3');

      const yesRadios = screen.getAllByLabelText('Yes');
      await user.click(yesRadios[0]); // Select yes for balcony/terrace

      // Submit form by clicking button since form role is not accessible
      const container = document.querySelector('form');
      if (container) {
        fireEvent.submit(container);
      }

      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          numberOfBedrooms: '3',
          balconyTerrace: YesNoOption.YES,
        })
      );
    });

    it('submits complete form data correctly', async () => {
      const user = userEvent.setup();
      render(<PropertyForm {...defaultProps} />);

      // Select user role
      const dropdownButtons = screen.getAllByText('Choose an option');
      await user.click(dropdownButtons[0]);
      await waitFor(() => screen.getByText(UserRole.OWNER_AND_OCCUPIER));
      await user.click(screen.getByText(UserRole.OWNER_AND_OCCUPIER));

      // Select ownership type
      const updatedDropdownButtons = screen.getAllByText('Choose an option');
      await user.click(updatedDropdownButtons[0]);
      await waitFor(() => screen.getByText(OwnershipType.FREEHOLD));
      await user.click(screen.getByText(OwnershipType.FREEHOLD));

      // Select property type
      const updatedDropdownButtons2 = screen.getAllByText('Choose an option');
      await user.click(updatedDropdownButtons2[0]); // First remaining "Choose an option"
      await waitFor(() => screen.getByText(PropertyType.HOUSE));
      await user.click(screen.getByText(PropertyType.HOUSE));

      // Select property sub type
      const finalDropdownButtons = screen.getAllByText('Choose an option');
      await user.click(finalDropdownButtons[0]); // Last remaining "Choose an option"
      await waitFor(() => screen.getByText(PropertySubType.DETACHED));
      await user.click(screen.getByText(PropertySubType.DETACHED));

      // Fill number inputs
      const numberInputs = screen.getAllByPlaceholderText('e.g. 2');
      await user.type(numberInputs[0], '3'); // bedrooms
      await user.type(numberInputs[1], '2'); // bathrooms
      await user.type(numberInputs[2], '2'); // floors

      // Fill area input with updated placeholder
      const areaInput = screen.getByPlaceholderText('e.g. 800');
      await user.type(areaInput, '120');

      // Select radio options
      const yesRadios = screen.getAllByLabelText('Yes');
      await user.click(yesRadios[0]); // balcony
      await user.click(yesRadios[1]); // garden

      const noRadios = screen.getAllByLabelText('No');
      await user.click(noRadios[2]); // swimming pool

      // Submit form
      const container = document.querySelector('form');
      if (container) {
        fireEvent.submit(container);
      }

      expect(mockOnSubmit).toHaveBeenCalledWith({
        userRole: UserRole.OWNER_AND_OCCUPIER,
        ownershipType: OwnershipType.FREEHOLD,
        propertyType: PropertyType.HOUSE,
        propertySubType: PropertySubType.DETACHED,
        numberOfBedrooms: '3',
        numberOfBathrooms: '2',
        numberOfFloors: '2',
        floorPropertyIsOn: '',
        grossInternalArea: '120',
        balconyTerrace: YesNoOption.YES,
        garden: YesNoOption.YES,
        swimmingPool: YesNoOption.NO,
        documents: [],
      });
    });

    it('handles empty initial data correctly', () => {
      render(<PropertyForm {...defaultProps} />);

      // Initially no property type is selected
      expect(screen.queryByDisplayValue(PropertyType.HOUSE)).not.toBeInTheDocument();

      // Now 4 dropdowns show by default (userRole, ownership, property type, property sub type)
      const placeholderButtons = screen.getAllByText('Choose an option');
      expect(placeholderButtons.length).toBe(4);
    });

    it('handles partial initial data correctly', () => {
      const partialData = {
        ownershipType: OwnershipType.LEASEHOLD,
        numberOfBedrooms: '2',
      };

      render(<PropertyForm {...defaultProps} initialData={partialData} />);

      expect(screen.getByText(OwnershipType.LEASEHOLD)).toBeInTheDocument();
      expect(screen.getByDisplayValue('2')).toBeInTheDocument();
    });
  });

  // Accessibility Tests
  describe('Accessibility', () => {
    it('has proper form structure', () => {
      const { container } = render(<PropertyForm {...defaultProps} />);

      const form = container.querySelector('form');
      expect(form).toBeInTheDocument();
    });

    it('has proper button for upload', () => {
      render(<PropertyForm {...defaultProps} />);

      const uploadButton = screen.getByRole('button', { name: /upload documents/i });
      expect(uploadButton).toBeInTheDocument();
    });

    it('has proper radio groups', () => {
      render(<PropertyForm {...defaultProps} />);

      const yesRadios = screen.getAllByLabelText('Yes');
      const noRadios = screen.getAllByLabelText('No');

      expect(yesRadios).toHaveLength(3);
      expect(noRadios).toHaveLength(3);
    });
  });

  // Edge Cases
  describe('Edge Cases', () => {
    it('handles undefined onSubmit gracefully', () => {
      render(<PropertyForm />);
      const container = document.querySelector('form');

      expect(() => {
        if (container) {
          fireEvent.submit(container);
        }
      }).not.toThrow();
    });
  });
});
