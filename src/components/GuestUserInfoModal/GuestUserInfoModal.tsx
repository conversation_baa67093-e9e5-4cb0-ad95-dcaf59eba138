import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { Modal } from '../Modal';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { ConfirmDialog } from '../ConfirmDialog';
import styles from './GuestUserInfoModal.module.scss';
import useJobSubmissionValidation from '@/hooks/useJobSubmissionValidation';
import { Input } from '@/components/Input';
import { GuestUserInfoStepper } from '@/components/GuestUserInfoModal/components/GuestUserInfoStepper/GuestUserInfoStepper';
import { useGuestConversion } from '@/hooks/useGuestConversion';
import { AddressType, AddressInput } from '@/components/AddressInput';
import useClerkDetailsHandler from '@/hooks/useClerkDetailsHandler';
import { useWidgets } from '@/hooks/useWidgets';
import useBackendClient from '@/hooks/useBackendClient';

interface UserInfoModalProps {
  open: boolean;
  onClose: () => void;
  onFinish: () => void;
}

export const GuestUserInfoModal: React.FC<UserInfoModalProps> = ({ open, onClose, onFinish }) => {
  const { isLoaded } = useUser();
  const { client } = useBackendClient();
  const [showCloseConfirmDialog, setShowCloseConfirmDialog] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [step, setStep] = useState(1);
  const [transitioning, setTransitioning] = useState(false);
  const { saveAddress } = useWidgets();
  const { convertGuest, error: guestConversionError } = useGuestConversion();
  const [address, setAddress] = useState<AddressType>();
  const { setPhone, error: userDetailsError } = useClerkDetailsHandler();

  useEffect(() => {
    if (open) {
      setStep(1);
    }
  }, [open]);

  const { areRequiredFieldsFilled } = useJobSubmissionValidation();

  const handleModalClose = useCallback(() => {
    if (!areRequiredFieldsFilled) {
      setShowCloseConfirmDialog(true);
      return;
    }

    onClose();
  }, [onClose, areRequiredFieldsFilled]);

  const handleConfirmClose = useCallback(() => {
    setShowCloseConfirmDialog(false);
    onClose();
  }, [onClose]);

  const handleCancelClose = useCallback(() => {
    setShowCloseConfirmDialog(false);
  }, []);

  useEffect(() => {
    setTransitioning(false);
  }, [step]);

  const canGoNext = useMemo(() => {
    if (transitioning) {
      return false;
    }
    switch (step) {
      case 1:
        return firstName && lastName && email;
      case 2:
        return !!address;
      case 3:
        return !!phoneNumber;
      default:
        throw new Error(`Invalid step: ${step}`);
    }
  }, [address, email, firstName, lastName, phoneNumber, step, transitioning]);

  const goNext = async () => {
    if (!canGoNext) {
      return;
    }
    setTransitioning(true);
    switch (step) {
      case 1:
        convertGuest({ firstName, lastName, email }).then((isConverted) => {
          isConverted && setStep(2);
          setTransitioning(false);
        });
        break;
      case 2:
        if (!address) {
          setTransitioning(false);
          return;
        }
        const saved = await saveAddress(address, client);
        if (saved) {
          setStep(3);
        }
        setTransitioning(false);
        break;
      case 3:
        setPhone(phoneNumber)
          .then(() => {
            onFinish();
          })
          .finally(() => {
            setTransitioning(false);
          });
        break;
    }
  };

  if (!isLoaded) {
    return (
      <Modal open={open} onClose={handleModalClose} title="Your details">
        <div>Loading...</div>
      </Modal>
    );
  }

  const forms = [
    <>
      <h2 className={styles.title}>Your details</h2>
      <p className={styles.note}>Here’s where you’ll receive your quotes.</p>
      <div className={styles.formContainer}>
        <div className={styles.grid}>
          <div className={styles.cell}>
            <div className={styles.label}>First name</div>
            <Input
              value={firstName}
              autoComplete="given-name"
              placeholderText="First name"
              onChange={setFirstName}
              disableClear={true}
              showHelperText={false}
              showLabel={false}
              fullWidth
            />
            {guestConversionError && guestConversionError.field === 'firstName' && (
              <div className={styles.error}>{guestConversionError.errorMessage}</div>
            )}
          </div>
          <div className={styles.cell}>
            <div className={styles.label}>Last name</div>
            <Input
              value={lastName}
              onChange={setLastName}
              autoComplete="family-name"
              placeholderText="Last name"
              disableClear={true}
              showHelperText={false}
              showLabel={false}
              fullWidth
            />
            {guestConversionError && guestConversionError.field === 'lastName' && (
              <div className={styles.error}>{guestConversionError.errorMessage}</div>
            )}
          </div>
          <div className={`${styles.cell} ${styles.mergedColumns}`}>
            <div className={styles.label}>Email</div>
            <Input
              value={email}
              onChange={setEmail}
              autoComplete="email"
              placeholderText="Your email"
              disableClear={true}
              showHelperText={false}
              showLabel={false}
              fullWidth
            />
            {guestConversionError &&
              (!guestConversionError.field || guestConversionError.field === 'email') && (
                <div className={styles.error}>{guestConversionError.errorMessage}</div>
              )}
            {userDetailsError?.field === 'email' && (
              <div className={styles.error}>{userDetailsError.errorMessage}</div>
            )}
          </div>
        </div>
      </div>
      <p className={`${styles.note} ${styles.marginBottom}`}>
        We’ll always send you the best quotes.
      </p>
    </>,
    <>
      <h2 className={styles.title}>Your address</h2>
      <p className={styles.note}>We use your postcode to find experts and prices near you.</p>
      <div className={styles.formContainer}>
        <AddressInput
          title=""
          onChange={setAddress}
          placeholder="Find address by postcode"
          manualEntryText="Enter address manually"
          hideBorder
          noPadding
          hideTitle
          compactMode
          forceDropdownPosition={'bottom'}
        />
      </div>
      <p className={`${styles.note} ${styles.marginBottom}`}>
        Your address is not shared with anyone.
      </p>
    </>,
    <>
      <h2 className={styles.title}>Your phone number</h2>
      <p className={styles.note}>We’ll message you as soon as updates come in.</p>
      <div className={styles.formContainer}>
        <div className={styles.cell}>
          <div className={styles.label}>Phone number</div>
          <Input
            value={phoneNumber}
            onChange={setPhoneNumber}
            autoComplete="phone"
            placeholderText={'+44'}
            disableClear={true}
            showHelperText={false}
            showLabel={false}
            fullWidth
          />
          {userDetailsError?.field === 'phoneNumber' && (
            <div className={styles.error}>{userDetailsError.errorMessage}</div>
          )}
        </div>
      </div>
      <p className={`${styles.note} ${styles.marginBottom}`}>
        No spam. We only contact you about the job.
      </p>
    </>,
  ];

  return (
    <Modal open={open} onClose={handleModalClose} title="">
      <div className={styles.stepperContainer}>
        <GuestUserInfoStepper currentStep={step} allSteps={3} />
      </div>
      {forms[step - 1]}
      <div className={styles.continueButton}>
        <Button
          type={ButtonType.PRIMARY}
          color={ButtonColor.GREEN_PRIMARY}
          size={ButtonSize.L}
          state={canGoNext ? ButtonState.DEFAULT : ButtonState.DISABLED}
          onClick={goNext}
        >
          {step === 3 ? 'Save details' : 'Next'}
        </Button>
      </div>
      <ConfirmDialog
        title="Missing details"
        description="We need more details to find you quotes."
        isOpen={showCloseConfirmDialog}
        onCancel={handleConfirmClose}
        onOk={handleCancelClose}
        cancelText="Leave"
        okText="Continue"
        cancelType="danger"
      />
    </Modal>
  );
};
