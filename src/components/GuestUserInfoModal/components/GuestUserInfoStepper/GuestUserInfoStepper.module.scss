.stepper {
  display: flex;
  align-items: center;
  width: 100%;
}

.steps {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-grow: 1;
  justify-content: space-between;
  margin: 0 auto;
}

.backButton {
  margin-right: var(--spacing-3-5);
}

.step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray-100);
}

.line {
  flex: 1 1 0;
  height: 4px;
  background: #d9d9d9;
  border-radius: 3px;
  margin: 0 4px 0 0;
}

.line:last-child {
  margin-right: 0;
}

.completed {
  background: #1b6e5a;
}

.active {
  background: var(--colors-yellow-500);
}

@media (max-width: 600px) {
  .stepper {
    gap: var(--spacing-0-5);
    width: 100%;
    min-width: 0;
  }

  .line {
    height: 3px;
    margin: 0 2px 0 0;
  }
}
