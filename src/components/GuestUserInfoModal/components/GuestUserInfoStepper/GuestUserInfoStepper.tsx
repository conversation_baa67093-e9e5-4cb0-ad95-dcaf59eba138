import React from 'react';
import classNames from 'classnames';
import styles from './GuestUserInfoStepper.module.scss';
import { useBreakpoint } from '@/utils/breakpointUtils';

export interface StepperProps {
  allSteps: number;
  currentStep: number;
  onBack?: () => void;
}

export const GuestUserInfoStepper: React.FC<StepperProps> = ({ allSteps, currentStep }) => {
  const { isMobile } = useBreakpoint();

  return (
    <div className={styles.stepper}>
      <div className={`${styles.steps} ${isMobile ? styles.mobile : ''}`}>
        {Array.from({ length: allSteps }).map((_, idx) => {
          const stepNumber = idx + 1;
          const isCompleted = stepNumber < currentStep;
          const isActive = stepNumber === currentStep;

          return (
            <div
              key={stepNumber}
              className={classNames(
                styles.line,
                { [styles.completed]: isCompleted },
                { [styles.active]: isActive }
              )}
            />
          );
        })}
      </div>
    </div>
  );
};