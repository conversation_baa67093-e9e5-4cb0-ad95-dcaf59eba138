$gap: var(--spacing-4);

.formContainer {
  max-width: 100%;
  overflow: visible;
  margin-top: $gap;
  margin-bottom: $gap;
}

.error {
  font-size: 14px;
  color: var(--colors-red-500);
  margin-top: 4px;
  margin-bottom: 8px;
}

.continueButton {
  display: flex;
  width: 100%;
  padding-bottom: 24px;

  @media (max-width: 767px) {
    padding-bottom: $gap;
  }
}

.guestDisabledField {
  opacity: 0.3;
  pointer-events: none;
}

.stepperContainer {
  margin-bottom: var(--spacing-4);
  margin-top: 52px;
  width: 100%;
}

.title {
  align-self: stretch;
  color: var(--black, #000);
  font-feature-settings: 'ss05' on, 'ss06' on, 'ss07' on, 'ss08' on, 'ss09' on, 'ss11' on;
  font-family: 'Ryker', Arial, sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 120%; /* 21.6px */
}

.note {
  align-self: stretch;
  color: var(--gray-600, #4B5563);
  font-family: Quasimoda;
  font-size: $gap;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 2 columns */
  gap: 8px;
}

.cell {
  display: flex;
  flex-direction: column;
}

.mergedColumns {
  grid-column: 1 / -1; /* span both columns */
}

.marginBottom {
  margin-bottom: $gap;
}

.label {
  color: var(--gray-900, #111928);
  font-family: Quasimoda;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 150%; /* 24px */
}