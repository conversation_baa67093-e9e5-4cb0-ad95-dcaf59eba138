import React from 'react';
import styles from './MessageDocumentPreview.module.scss';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import * as StrokeRounded from '@hugeicons-pro/core-stroke-rounded';
import { IconSvgObject } from '@hugeicons/react';
import { RemoveFileButton } from '@/components/RemoveFileButton';
import { Spinner } from '@/components/Spinner/Spinner';
import { UniversalFile } from '@/types/file';

interface DocumentPreviewProps {
  file: UniversalFile;
  index: number;
  onRemove?: () => void;
}

export const MessageDocumentPreview: React.FC<DocumentPreviewProps> = ({
  file,
  index,
  onRemove,
}) => {
  const fileExtension = file.name.split('.').pop() || '';
  const filename = file.name;

  return (
    <div key={index} className={styles.documentPreviewContainer}>
      <div className={styles.documentPreviewIcon}>
        {file.status === 'uploading' ? (
          <Spinner color="#fff" size={20} />
        ) : (
          <HugeiconsIcon
            icon={StrokeRounded.Files01Icon as unknown as IconSvgObject}
            size={20}
            color="#fff"
          />
        )}
      </div>
      <div className={styles.documentContent}>
        <div className={styles.documentName} title={filename}>
          {filename}
        </div>
        <div className={styles.documentExtension}>{fileExtension}</div>
      </div>

      {onRemove && <RemoveFileButton onRemove={onRemove} className={styles.removeButton} />}
    </div>
  );
};
