import { UniversalFile } from '@/types/file';

export interface FileUploadManagerProps {
  files: UniversalFile[];
  onFileUpload: (file: File) => Promise<void>;
  onFileRemove: (fileId: string | number) => void;
  onFileRetry?: (file: UniversalFile) => void;
  disabled?: boolean;
  maxFiles?: number;
  acceptedFileTypes?: string[];
  className?: string;
  showPreview?: boolean;
  gridLayout?: boolean;
}

export interface FileUploadGridProps {
  files: UniversalFile[];
  onFileRemove?: (fileId: string | number) => void;
  onFileRetry?: (file: UniversalFile) => void;
  className?: string;
  showRemoveButton?: boolean;
}
