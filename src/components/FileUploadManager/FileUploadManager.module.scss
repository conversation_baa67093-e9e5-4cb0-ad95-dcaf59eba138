.container {
  position: relative;
  width: 100%;
}

.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.dragging {
  .dropZone {
    display: flex;
  }
}

.dropZone {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(34, 197, 94, 0.1);
  border: 2px dashed #22c55e;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.dropContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #22c55e;
  font-weight: 500;
}

.uploadSection {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hiddenInput {
  display: none;
}

.grid {
  margin-top: 16px;
}

.gridLayout {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}
