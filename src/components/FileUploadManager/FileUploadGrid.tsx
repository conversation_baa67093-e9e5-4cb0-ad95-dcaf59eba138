import React from 'react';
import classNames from 'classnames';
import { FileUploadPreview } from '@/components/Composer/components/FileUploadPreview/FileUploadPreview';
import { MessageDocumentPreview } from '@/components/FileUploadManager/components/MessageDocumentPreview';
import { FileUploadGridProps } from './FileUploadManager.types';
import { getFileId } from '@/utils/fileUtils';
import styles from './FileUploadGrid.module.scss';

export const FileUploadGrid: React.FC<FileUploadGridProps> = ({
  files,
  onFileRemove,
  onFileRetry,
  className,
  showRemoveButton = true,
}) => {
  if (files.length === 0) return null;

  return (
    <div className={classNames(styles.grid, className)}>
      {files.map((file, index) => {
        const fileId = getFileId(file);
        const handleRemove =
          showRemoveButton && onFileRemove ? () => onFileRemove(fileId) : undefined;
        const handleRetry = onFileRetry ? () => onFileRetry(file) : undefined;

        return file.type === 'image' ? (
          <FileUploadPreview
            key={`${fileId}-${file.name}`}
            file={file}
            onRemove={handleRemove}
            onRetry={handleRetry}
          />
        ) : (
          <MessageDocumentPreview
            key={`${fileId}-${file.name}`}
            file={file}
            index={index}
            onRemove={handleRemove}
          />
        );
      })}
    </div>
  );
};
