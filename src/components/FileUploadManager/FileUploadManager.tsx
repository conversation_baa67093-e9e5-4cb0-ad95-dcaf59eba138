import React, { useRef, useState } from 'react';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { Button } from '@/components/Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '@/components/Button/Button.types';
import { Attachment01Icon } from '@hugeicons/react';
import { FileUploadManagerProps } from './FileUploadManager.types';
import { FileUploadGrid } from './FileUploadGrid';
import { isValidFile, ALL_SUPPORTED_TYPES } from '@/utils/fileUtils';
import styles from './FileUploadManager.module.scss';

export const FileUploadManager: React.FC<FileUploadManagerProps> = ({
  files,
  onFileUpload,
  onFileRemove,
  onFileRetry,
  disabled = false,
  maxFiles,
  acceptedFileTypes,
  className,
  showPreview = true,
  gridLayout = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  const debouncedSetIsDragging = debounce(setIsDragging, 100);

  const validateAndProcessFiles = async (fileList: FileList | File[]) => {
    if (disabled) return;

    const filesArray = Array.from(fileList);
    const validFiles = filesArray.filter((file) => {
      if (acceptedFileTypes) {
        const extension = `.${file.name.split('.').pop()?.toLowerCase()}`;
        return acceptedFileTypes.includes(extension);
      }
      return isValidFile(file);
    });

    for (const file of validFiles) {
      if (maxFiles && files.length >= maxFiles) break;
      await onFileUpload(file);
    }
  };

  const handleFileSelect = () => {
    if (!disabled) fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (selectedFiles) {
      validateAndProcessFiles(selectedFiles);
      event.target.value = '';
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
    debouncedSetIsDragging(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      validateAndProcessFiles(droppedFiles);
    }
  };

  const handleDragEnter = (e: React.DragEvent) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
    debouncedSetIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      debouncedSetIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (disabled) return;
    e.preventDefault();
    e.stopPropagation();
  };

  const acceptValue = acceptedFileTypes?.join(',') || ALL_SUPPORTED_TYPES;

  return (
    <div
      className={classNames(styles.container, className, {
        [styles.disabled]: disabled,
        [styles.dragging]: isDragging,
      })}
      onDrop={handleDrop}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
    >
      {isDragging && !disabled && (
        <div className={styles.dropZone}>
          <div className={styles.dropContent}>
            <Attachment01Icon size={24} color="#22C55E" />
            <span>Drop files here to upload</span>
          </div>
        </div>
      )}

      <div className={styles.uploadSection}>
        <Button
          type={ButtonType.TERTIARY}
          size={ButtonSize.XS}
          color={ButtonColor.GREEN_PRIMARY}
          iconOnly
          leftIcon={<Attachment01Icon />}
          showLeftIcon
          onClick={handleFileSelect}
          aria-label="Upload files"
          state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
        />

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptValue}
          onChange={handleFileChange}
          disabled={disabled}
          className={styles.hiddenInput}
        />
      </div>

      {showPreview && files.length > 0 && (
        <FileUploadGrid
          files={files}
          onFileRemove={onFileRemove}
          onFileRetry={onFileRetry}
          showRemoveButton={true}
          className={classNames(styles.grid, {
            [styles.gridLayout]: gridLayout,
          })}
        />
      )}
    </div>
  );
};
