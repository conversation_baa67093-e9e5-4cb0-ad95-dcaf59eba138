'use client';

import { useMemo } from 'react';
import './Typography.scss';

interface TypographyProps {
  variant:
    | 'h1'
    | 'h2'
    | 'h3'
    | 'h4'
    | 'h5'
    | 'h6'
    | 'body-l'
    | 'body'
    | 'body-s'
    | 'body-xs'
    | 'body-xss'
    | 'body-l-bold'
    | 'body-bold'
    | 'body-s-bold';
  children: React.ReactNode;
  font?: 'quasimoda' | 'ryker';
  className?: string;
}

export const Typography = ({
  variant,
  children,
  font = 'ryker',
  className = '',
}: TypographyProps) => {
  const Component = variant.startsWith('h')
    ? (variant as keyof JSX.IntrinsicElements)
    : ('p' as keyof JSX.IntrinsicElements);

  const classNames = useMemo(() => {
    const tempClassNames: string[] = [variant, font];

    if (className) {
      tempClassNames.push(className);
    }

    return tempClassNames.join(' ').trim();
  }, [variant, font, className]);

  return <Component className={classNames}>{children}</Component>;
};
