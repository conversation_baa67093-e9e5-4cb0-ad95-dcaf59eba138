@use '../../styles/abstracts/mixins' as mixins;

.ryker {
  @include mixins.font-primary;
}

.quasimoda {
  @include mixins.font-heading;
}

// Headings
.h1 {
  @include mixins.h1;
}

.h2 {
  @include mixins.h2;
}

.h3 {
  @include mixins.h3;
}

.h4 {
  @include mixins.h4;
}

.h5 {
  @include mixins.h5;
}

.h6 {
  @include mixins.h6;
}

// Body text
.body-l {
  @include mixins.body-l;
}

.body {
  @include mixins.body;
}

.body-s {
  @include mixins.body-s;
}

.body-xs {
  @include mixins.body-xs;
}

.body-xss {
  @include mixins.body-xss;
}

// Bold variants
.body-l-bold {
  font-weight: 700;
  @include mixins.body-l;
}

.body-bold {
  font-weight: 700;
  @include mixins.body;
}

.body-s-bold {
  font-weight: 700;
  @include mixins.body-s;
} 