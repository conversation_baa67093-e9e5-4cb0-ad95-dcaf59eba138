import React from 'react';
import { render } from '@testing-library/react';
import { vi } from 'vitest';
import { FirstPromptPage } from '../FirstPromptPage';

vi.mock('@/components/Composer', () => ({
  Composer: () => <div data-testid="composer">Composer</div>,
}));

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('@clerk/nextjs', () => ({
  useUser: () => ({
    user: {
      firstName: 'John',
    },
  }),
  useAuth: () => ({
    getToken: vi.fn(),
  }),
}));

vi.mock('@hugeicons/react', () => ({
  House04Icon: () => <div data-testid="house-icon">House Icon</div>,
}));

describe('FirstPromptPage Snapshots', () => {
  it('matches default snapshot', () => {
    const { container } = render(<FirstPromptPage />);
    expect(container).toMatchInlineSnapshot(`
      <div>
        <div
          class="_container_aa1efd"
        >
          <main
            class="_content_aa1efd"
          >
            <h1
              class="_title_aa1efd"
            >
              Hey
               John
              ,
            </h1>
            <h2
              class="_subtitle_aa1efd"
            >
              What can I help you with?
            </h2>
            <div
              class="_composerWrapper_aa1efd"
            >
              <div
                data-testid="composer"
              >
                Composer
              </div>
            </div>
            <div
              class="_quickActions_aa1efd"
            >
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Help with an issue
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  DIY instructions and support
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Find me a tradesperson
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Research home improvement
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Questions about home management
                </span>
              </button>
            </div>
          </main>
        </div>
      </div>
    `);
  });

  it('matches snapshot with custom className', () => {
    const { container } = render(<FirstPromptPage className="custom-class" />);
    expect(container).toMatchInlineSnapshot(`
      <div>
        <div
          class="_container_aa1efd custom-class"
        >
          <main
            class="_content_aa1efd"
          >
            <h1
              class="_title_aa1efd"
            >
              Hey
               John
              ,
            </h1>
            <h2
              class="_subtitle_aa1efd"
            >
              What can I help you with?
            </h2>
            <div
              class="_composerWrapper_aa1efd"
            >
              <div
                data-testid="composer"
              >
                Composer
              </div>
            </div>
            <div
              class="_quickActions_aa1efd"
            >
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Help with an issue
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  DIY instructions and support
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Find me a tradesperson
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Research home improvement
                </span>
              </button>
              <button
                class="_button_ee73f7 _secondary_ee73f7 _l_ee73f7 _green-primary_ee73f7 _withText_ee73f7"
              >
                <span
                  class="_text_ee73f7 _text-l_ee73f7 _multiLine_ee73f7"
                >
                  Questions about home management
                </span>
              </button>
            </div>
          </main>
        </div>
      </div>
    `);
  });
});
