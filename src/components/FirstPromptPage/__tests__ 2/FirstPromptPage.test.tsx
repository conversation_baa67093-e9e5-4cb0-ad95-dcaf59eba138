import React from 'react';
import { render } from '@testing-library/react';
import { FirstPromptPage } from '../FirstPromptPage';
import { vi } from 'vitest';

// Mock heavy Composer to avoid side effects
vi.mock('@/components/Composer', () => ({
  Composer: ({ className }: { className?: string }) => (
    <div data-testid="composer" className={className}>ComposerMock</div>
  ),
}));

// Also mock document upload hook to avoid timers/intervals
vi.mock('@/hooks/useSWRDocumentUpload', () => ({
  useSWRDocumentUpload: vi.fn(() => ({ uploadDocument: vi.fn(), isUploading: false, error: null })),
}));

vi.mock('ezheaders', () => ({
  default: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn()
  }
}));

vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    getToken: vi.fn(() => Promise.resolve('mock-token')),
    isLoaded: true,
    isSignedIn: true,
    userId: 'mock-user-id'
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
    isLoaded: true,
    isSignedIn: true
  })
}));

vi.mock('@/hooks/useChats', () => ({
  useChats: () => ({
    sendMessage: vi.fn(),
    setActiveChatId: vi.fn(),
    addChat: vi.fn(),
    isLoading: false
  })
}));

vi.mock('@/hooks/useMessages', () => ({
  useMessages: () => ({
    messages: [],
    addMessage: vi.fn()
  })
}));

vi.mock('@/hooks/useUploads', () => ({
  useUploads: () => ({
    uploadDocument: vi.fn(),
    deleteDocument: vi.fn()
  })
}));

vi.mock('@/hooks/useUser', () => ({
  useUser: vi.fn()
}));

vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

const TestProvider = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

describe('FirstPromptPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('applies custom className when provided', () => {
    const customClass = 'custom-class';
    const { container } = render(
      <TestProvider>
        <FirstPromptPage className={customClass} />
      </TestProvider>
    );
    expect(container.firstChild).toHaveClass(customClass);
  });
});
