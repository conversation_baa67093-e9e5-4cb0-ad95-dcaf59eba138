import React, { useEffect, useRef } from 'react';
import { Composer } from '@/components/Composer';
import { Button } from '@/components/Button';
import { ButtonColor, ButtonSize, ButtonType } from '@/components/Button/Button.types';
import classNames from 'classnames';
import styles from './FirstPromptPage.module.scss';
import { FirstPromptPageProps } from './FirstPromptPage.types';
import { useUser } from '@clerk/nextjs';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { House04Icon } from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import { useWidgets } from '@/hooks/useWidgets';
import useMessageSender from '@/hooks/useMessageSender';
import { useAuth } from '@clerk/nextjs';
import { useSearchParams } from 'next/navigation';

export const FirstPromptPage = ({ className }: FirstPromptPageProps): JSX.Element => {
  const userData = useUser();
  const user = userData?.user;
  const { sendMessage } = useMessageSender();
  const { isMobile } = useBreakpoint();
  const { addressValue } = useWidgets();
  const { isSignedIn } = useAuth();
  const searchParams = useSearchParams();
  const queryParam = searchParams?.get('query') || '';
  const hasSentMessage = useRef(false);

  useEffect(() => {
    if (queryParam && !hasSentMessage.current) {
      // For authenticated users, check if they've completed onboarding
      if (isSignedIn) {
        const hasCompletedOnboarding = user?.unsafeMetadata?.visitedOnboarding === true;
        if (hasCompletedOnboarding) {
          hasSentMessage.current = true;
          sendMessage(queryParam);
        }
      } else {
        // For guest users, send message immediately
        hasSentMessage.current = true;
        sendMessage(queryParam);
      }
    }
  }, [isSignedIn, queryParam, sendMessage, user]);

  const quickActions = [
    'Help with an issue',
    'DIY instructions and support',
    'Find me a tradesperson',
    'Research home improvement',
    'Questions about home management',
  ];

  return (
    <div className={classNames(styles.container, className)}>
      <main className={styles.content}>
        {addressValue && (
          <header className={styles.header}>
            <div className={styles.address}>
              <div className={styles.addressIcon}>
                <HugeiconsIcon icon={House04Icon as unknown as IconSvgObject} size={24} />
              </div>
              <span>{addressValue}</span>
            </div>
          </header>
        )}
        <h1 className={styles.title}>Hey{user?.firstName ? ` ${user.firstName}` : ''},</h1>
        <h2 className={styles.subtitle}>What can I help you with?</h2>

        <div className={styles.composerWrapper}>
          <Composer placeholder="Message Alfie" defaultValue={queryParam} />
        </div>

        <div className={styles.quickActions}>
          {quickActions.map((title) => (
            <Button
              multiLine
              key={title}
              type={ButtonType.SECONDARY}
              size={isMobile ? ButtonSize.BASE : ButtonSize.L}
              color={ButtonColor.GREEN_PRIMARY}
              onClick={() => {
                sendMessage(title);
              }}
            >
              {title}
            </Button>
          ))}
        </div>
      </main>
    </div>
  );
};
