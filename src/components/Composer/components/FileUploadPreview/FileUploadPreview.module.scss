.preview {
  position: relative;
  width: 68px;
  height: 68px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: background-color 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--colors-white);
    border-radius: 8px;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(75, 85, 99, 0.3);
    border-radius: 8px;
    z-index: 1;
  }

  &.uploaded {
    &::after {
      background: var(--colors-gray-600);
    }
  }

  &.success {
    .previewImage {
      opacity: 1;
    }

    .loaderContainer {
      display: none;
    }
  }

  .previewImage {
    opacity: 0;
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: 8px;
    position: relative;
    width: 100%;
    height: 100%;
  }

  &.error::after {
    background: #4b5563;
    opacity: 0.5;
  }
}

.loaderContainer {
  width: 32px;
  height: 32px;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid var(--colors-white);
}

.progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid #d28e28;
  clip-path: polygon(50% 50%, 50% 0, 100% 0, 100% 50%, 50% 50%);
  transform: rotate(calc(var(--progress) * 3.6deg));
  transform-origin: center;
  transition: transform 0.3s ease;
}

.filename {
  display: none;
  font-size: 12px;
  color: var(--colors-gray-700);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 64px;
  text-align: center;
  z-index: 2;
}

.removeButton {
  position: absolute;
  top: 4px;
  right: 4px;
  z-index: 2;
  border: 1px solid var(--colors-white);
  color: var(--colors-white);
}

.attachments {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  padding: 8px 16px;
  overflow-x: auto;
  width: 100%;

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--colors-neutral-200);
    border-radius: 3px;
  }
}

.retryButton {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  border: 1px solid var(--colors-white);
  color: var(--colors-white);
}
