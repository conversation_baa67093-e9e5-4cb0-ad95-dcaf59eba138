import { render, screen } from '@testing-library/react';
import { Header } from '../Header';
import React from 'react';
import { vi } from 'vitest';

vi.mock('next/link', () => ({
  default: ({ children, href }: { children: React.ReactNode; href: string }) => {
    return <a href={href}>{children}</a>;
  },
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: () => ({
    isSignedIn: true,
  }),
  useUser: () => ({
    isLoaded: true,
    user: {
      id: 'test-user-id',
      firstName: 'Test',
      lastName: 'User',
      username: 'testuser',
      primaryEmailAddress: {
        emailAddress: '<EMAIL>',
      },
    },
  }),
  useClerk: () => ({
    signOut: vi.fn(),
    openUserProfile: vi.fn(),
  }),
  SignInButton: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  SignUpButton: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('@/utils/breakpointUtils', () => ({
  useBreakpoint: () => ({
    isMobile: false,
  }),
}));

vi.mock('@/hooks/useSidebar', () => ({
  useSidebar: () => ({
    toggleSidebar: vi.fn(),
  }),
}));

vi.mock('@/components/CustomUserButton', () => ({
  CustomUserButton: () => <div data-testid="custom-user-button">CustomUserButton</div>,
}));

describe('Header', () => {
  it('renders the CustomUserButton component', () => {
    render(<Header />);

    const userButton = screen.getByTestId('custom-user-button');
    expect(userButton).toBeInTheDocument();
  });
});
