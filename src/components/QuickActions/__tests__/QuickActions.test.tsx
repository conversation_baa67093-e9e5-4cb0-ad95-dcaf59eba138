import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import { QuickActions } from '../QuickActions';
import { useChats } from '@/hooks/useChats';
import { useParams, useRouter } from 'next/navigation';
import useMessageSender from '@/hooks/useMessageSender';

// Mock useAuth and useUser from @clerk/nextjs
vi.mock('@/app/store', () => ({
  useAppSelector: vi.fn().mockImplementation((selector) =>
    selector({
      prompts: {
        currentId: null,
        prompts: {},
      },
      user: {
        hasCompletedOnboarding: true,
      },
    })
  ),
  useAppDispatch: vi.fn().mockReturnValue(vi.fn()),
}));

vi.mock('@clerk/nextjs', () => ({
  useAuth: vi.fn().mockReturnValue({
    getToken: vi.fn().mockResolvedValue('mock-token'),
  }),
  useUser: () => ({
    user: {
      firstName: 'Test User',
    },
  }),
}));

vi.mock('@/hooks/useChats');
vi.mock('@/hooks/useMessageSender');
vi.mock('next/navigation', () => ({
  useParams: vi.fn(() => ({ chatId: 'test-chat-id' })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  })),
  useSearchParams: vi.fn(() => ({
    get: vi.fn(() => null),
    has: vi.fn(() => false),
    getAll: vi.fn(() => []),
    keys: vi.fn(() => []),
    values: vi.fn(() => []),
    entries: vi.fn(() => []),
    forEach: vi.fn(),
    append: vi.fn(),
    delete: vi.fn(),
    set: vi.fn(),
    sort: vi.fn(),
    toString: vi.fn(() => ''),
  })),
  usePathname: vi.fn(() => '/'),
  redirect: vi.fn(),
  notFound: vi.fn(),
}));

describe('QuickActions', () => {
  const mockButtons = [
    { text: 'Button 1', value: 'value1' },
    { text: 'Button 2', value: 'value2' },
  ];

  const mockSendMessage = vi.fn();
  const mockSetActiveChatId = vi.fn();
  const mockPush = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useChats as unknown as jest.Mock).mockReturnValue({
      setActiveChatId: mockSetActiveChatId,
    });
    (useMessageSender as jest.Mock).mockReturnValue({
      sendMessage: mockSendMessage,
    });
    (useRouter as unknown as jest.Mock).mockReturnValue({ push: mockPush });
    (useParams as unknown as jest.Mock).mockReturnValue({});
  });

  it('renders all buttons', () => {
    render(<QuickActions buttons={mockButtons} />);
    expect(screen.getByText('Button 1')).toBeInTheDocument();
    expect(screen.getByText('Button 2')).toBeInTheDocument();
  });

  it('sends message when clicked in existing chat', async () => {
    (useParams as jest.Mock).mockReturnValue({ chatId: '123' });
    render(<QuickActions buttons={mockButtons} />);

    fireEvent.click(screen.getByText('Button 1'));
    expect(mockSendMessage).toHaveBeenCalledWith('value1');
  });
});
