.toggler {
  // Inherit base styles from Checkbox
  &:global(.checkbox-field) {
    gap: var(--spacing-2);
  }

  // Override text styles
  .label {
    font-family: Quasimoda;
    font-size: 18px;
    font-weight: 400;
    line-height: 27px;
    text-align: left;
  }

  .labelSm {
    font-size: 16px;
    line-height: 24px;
  }

  .labelDisabled {
    color: var(--colors-gray-400);
  }

  .helper {
    font-family: Quasimoda;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }

  .helperSm {
    font-size: 12px;
    line-height: 18px;
  }

  .helperDisabled {
    color: var(--colors-gray-400);
  }

  .toggle-control {
    position: relative;
    cursor: inherit;
    display: inline-block;
  }

  .input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: inherit;
  }

  .track {
    border-radius: 40px;
    background-color: var(--colors-gray-200);
    transition: background-color 0.2s;
  }

  .thumb {
    position: absolute;
    background-color: var(--colors-white);
    border-radius: 40px;
    transition: left 0.2s;
  }

  // Size variants
  &.sm {
    .track {
      width: var(--spacing-10);
      height: var(--spacing-5);
    }

    .thumb {
      width: 16px;
      height: 16px;
      top: 2px;
      left: 2px;
    }

    &.checked .thumb {
      left: 22px;
    }

    // Override text styles
    .label {
      font-size: 16px;
      line-height: 24px;
    }

    .helper {
      font-size: 12px;
      line-height: 18px;
    }
  }

  &.default {
    .track {
      width: var(--spacing-11);
      height: var(--spacing-6);
    }

    .thumb {
      width: 20px;
      height: 20px;
      top: 2px;
      left: 2px;
    }

    &.checked .thumb {
      left: 22px;
    }
  }

  &.lg {
    .track {
      width: var(--spacing-14);
      height: var(--spacing-7);
    }

    .thumb {
      width: 22px;
      height: 22px;
      top: 3px;
      left: 3px;
    }

    &.checked .thumb {
      left: 31px;
    }
  }

  // State variants
  &.checked .track {
    background-color: var(--colors-green-500);
  }

  &.disabled {
    .track {
      background-color: var(--colors-gray-200);
    }

    // Override text styles
    .label {
      color: var(--colors-gray-400);
    }

    .helper {
      color: var(--colors-gray-400);
    }
  }
} 