import React from "react";
import classNames from "classnames";
import { Checkbox } from "../Checkbox/Checkbox";
import { CheckboxState } from "../Checkbox/Checkbox.types";
import { TogglerProps, TogglerSize } from "./Toggler.types";
import styles from "./Toggler.module.scss";

export const Toggler = ({
  size = TogglerSize.DEFAULT,
  className,
  checked = false,
  onChange,
  state = CheckboxState.NORMAL,
  labelClassName,
  helperClassName,
  ...checkboxProps
}: TogglerProps): JSX.Element => {
  const togglerClasses = classNames(
    styles.toggler,
    styles[size.toLowerCase()],
    {
      [styles.checked]: checked,
      [styles.disabled]: state === CheckboxState.DISABLED,
    },
    className
  );

  const labelClasses = classNames(
    styles.label,
    {
      [styles.labelSm]: size === TogglerSize.SM,
      [styles.labelDisabled]: state === CheckboxState.DISABLED,
    },
    labelClassName
  );

  const helperClasses = classNames(
    styles.helper,
    {
      [styles.helperSm]: size === TogglerSize.SM,
      [styles.helperDisabled]: state === CheckboxState.DISABLED,
    },
    helperClassName
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (state !== CheckboxState.DISABLED && onChange) {
      onChange(e.target.checked);
    }
  };

  return (
    <Checkbox
      {...checkboxProps}
      checked={checked}
      onChange={onChange}
      state={state}
      className={togglerClasses}
      labelClassName={labelClasses}
      helperClassName={helperClasses}
      customControl={
        <div className={styles["toggle-control"]}>
          <input
            type="checkbox"
            checked={checked}
            onChange={handleChange}
            disabled={state === CheckboxState.DISABLED}
            className={styles.input}
          />
          <div className={styles.track} />
          <div className={styles.thumb} />
        </div>
      }
    />
  );
};
